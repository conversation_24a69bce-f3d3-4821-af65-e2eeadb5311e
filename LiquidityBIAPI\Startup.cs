using ExternalServices;
using Microsoft.OpenApi.Models;
using Microsoft.AspNetCore.HttpOverrides;
using GamesEngine.Settings;
using GamesEngineMocks;
using Puppeteer.EventSourcing;
using GamesEngine.Business.Liquidity.Persistence;
using System.Diagnostics;
using GamesEngine.Business.Liquidity.ExternalServices;

namespace LiquidityBIAPI
{
    public class Startup
    {
        public Startup(IWebHostEnvironment env, IConfiguration configuration)
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(env.ContentRootPath)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile("customization/appsettings.accounting.json", optional: true)
                .AddJsonFile("customization/appsettings.security.json", optional: true)
                .AddJsonFile("customization/appsettings.kafka.json", optional: true)
                .AddJsonFile("customization/appsettings.error_emails.json", optional: true)
                .AddJsonFile("customization/appsettings.apm.json", optional: true)
                .AddJsonFile("secrets/appsettings.secrets.json", optional: true);
            Configuration = builder.Build();
            Environment = env;
        }

        public IConfiguration Configuration { get; }
        public IWebHostEnvironment Environment { get; }

        public void ConfigureServices(IServiceCollection services)
        {
            var biHistoricalConfig = Configuration.GetSection("BIIntegration").GetSection("DBHistorical");
            var connectionStrings = biHistoricalConfig.GetSection("ConnectionStrings");
            string olapConnStr = connectionStrings.GetValue<string>("ClickHouse");
            string searchEngineConnStr = connectionStrings.GetValue<string>("Elasticsearch");

            if (!string.IsNullOrEmpty(olapConnStr))
            {
                services.AddSingleton<IDbConnectionFactory>(provider =>
                    new OlapConnectionFactory(olapConnStr)
                );
                services.AddSingleton<IOlapSchemaManager, OlapSchemaManager>();
                services.AddSingleton<IOlapRepository, OlapRepository>();
                services.AddSingleton<IOlapQueryService, OlapQueryService>();
            }
            if (!string.IsNullOrEmpty(searchEngineConnStr))
            {
                services.AddScoped<SearchStorage>(provider => new SearchStorage(searchEngineConnStr));
                services.AddScoped<ISearchStorage, SearchStorage>(provider => provider.GetRequiredService<SearchStorage>());
            }

            // Configure NodeExplorerClient with HttpClient
            var nodeExplorerUrl = Configuration.GetSection("InvoicePayment").GetValue<string>("NodeExplorerUrl");
            if (!string.IsNullOrEmpty(nodeExplorerUrl))
            {
                services.AddHttpClient<INodeExplorerClient, NodeExplorerClient>(client =>
                {
                    client.BaseAddress = new Uri(nodeExplorerUrl);
                });
            }

            services.AddControllers();
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "LiquidityBIAPI", Version = "v1" });
            });

            Security.Configure(services, Configuration);

            PaymentManager.Configure(Configuration);

            var biIntegration = Configuration.GetSection("BIIntegration");
            Debug.WriteLine($"biIntegration {biIntegration}");

            Integration.ConfigureLiquidityBI(biIntegration);
            Debug.WriteLine($"Integration is ready.");

            services.AddMvc(options => options.EnableEndpointRouting = false);

            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
        }

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, IOlapSchemaManager olapSchemaManager = null, IOlapRepository olapRepository = null, ISearchStorage elasticsearchQueryStore = null)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
                app.UseSwagger();
                app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "LiquidityBIAPI v1"));
            }

            var sectionDairy = Configuration.GetSection("DBDairy");
            var mySQL = sectionDairy.GetSection("ConnectionStrings").GetValue<string>("MySQL");
            var sqlServer = sectionDairy.GetSection("ConnectionStrings").GetValue<string>("SQLServer");
            var dbSelected = sectionDairy.GetValue<string>("DBSelected");
            var scriptBeforeRecovering = Configuration.GetValue<string>("ActionsBeforeRecovering");

            DBDairy dbDairy = new DBDairy();
            dbDairy.DBSelected = dbSelected;
            Integration.DbDairy = dbDairy;

            if (Integration.UseKafka) 
            {
                Consumers.CreateConsumerForOlap(olapRepository, olapSchemaManager);
                Consumers.CreateConsumerForSearchEngine(elasticsearchQueryStore);
            }

            int numberOfTheMockConfigured = -1;
            if (dbSelected == DatabaseType.MySQL.ToString())
            {
                LiquidityBIAPI.LiquidityBI.EventSourcingStorage(DatabaseType.MySQL, mySQL, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);
            }
            else if (dbSelected == DatabaseType.SQLServer.ToString())
            {
                LiquidityBIAPI.LiquidityBI.EventSourcingStorage(DatabaseType.SQLServer, sqlServer, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);
            }
            else if (!String.IsNullOrWhiteSpace(dbSelected))
            {
                throw new Exception($"There is no connection for {dbSelected}");
            }
            else
            {
                string mockToStartStr = Configuration["MockToStart"];
                if (!string.IsNullOrEmpty(mockToStartStr) && int.TryParse(mockToStartStr, out int parsedMockIndex))
                {
                    numberOfTheMockConfigured = parsedMockIndex;
                }
                else if (!string.IsNullOrEmpty(mockToStartStr))
                {
                    Debug.WriteLine($"Warning: MockToStart value '{mockToStartStr}' is not a valid integer. Mocks may not run as expected.");
                }

                var DOTNET_RUNNING_IN_CONTAINER = bool.Parse(System.Environment.GetEnvironmentVariable("DOTNET_RUNNING_IN_CONTAINER") ?? "false");
                if (DOTNET_RUNNING_IN_CONTAINER)
                    LiquidityBIAPI.LiquidityBI.EventSourcingStorage(DatabaseType.MySQL, mySQL, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);

                RunMock(LiquidityBIAPI.LiquidityBI.Actor, numberOfTheMockConfigured);
            }

            app.UseMvc();

            ForwardedHeadersOptions options = new ForwardedHeadersOptions();
            options.ForwardedHeaders = ForwardedHeaders.XForwardedFor;
            app.UseForwardedHeaders(options);

            string mockToStartValue = Configuration["MockToStart"];

            int currentMockSetting = -1;
            if (!string.IsNullOrEmpty(mockToStartValue) && int.TryParse(mockToStartValue, out int parsedVal))
            {
                currentMockSetting = parsedVal;
            }

            if (currentMockSetting == 0)
            {
                try
                {
                    if (olapSchemaManager != null)
                    {
                        Debug.WriteLine("Clearing ClickHouse tables for kind 'FP'...");
                        olapSchemaManager.DropAllTables("FP");
                        olapSchemaManager.CreateTablesIfNotExists("FP"); 
                        Debug.WriteLine("ClickHouse tables cleared and recreated.");
                    }
                    if (elasticsearchQueryStore != null)
                    {
                        Debug.WriteLine("Clearing Elasticsearch indices for kind 'FP'...");
                        elasticsearchQueryStore.DropAllTables("FP");
                        elasticsearchQueryStore.CreateTablesIfNotExists("FP");
                        Debug.WriteLine("Elasticsearch indices cleared and recreated.");
                    }
                    DataSeeder.SeedInitialData(olapRepository, elasticsearchQueryStore, "FP");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"An error occurred while clearing/reseeding initial data: {ex.Message}", ex);
                }
            }
            else
            {
                Debug.WriteLine($"MockToStart is '{mockToStartValue}' (parsed as {currentMockSetting}), skipping initial data clearing and reseeding.");
            }
        }

        void RunMock(Actor actor, int index = -1)
        {
            switch (index)
            {
                case 0:
                    LiquidityBIMocks.Init(actor);
                    Debug.WriteLine("Mock 0 initialized.");
                    break;
                default:
                    Debug.WriteLine($"Mock with index {index} is not implemented. Throwing exception.");
                    throw new Exception($"The mock {index} its not implemented yet.");
            }
        }
    }
}
