using System;
using System.Net.Http;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using System.Text.Json;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using System.Linq;
using System.Net;
using NBitcoin;
using NBitcoin.DataEncoders;
using NBitcoin.RPC;
using Newtonsoft.Json.Linq;
using System.IO;
using System.Collections.Generic;

namespace GamesEngine.Business.Liquidity.ExternalServices
{
    public class NodeExplorerClient : INodeExplorerClient
    {
        private readonly HttpClient _httpClient;
        private readonly string _explorerBaseUrl;
        private const decimal SatsPerBtc = 100_000_000m;
        private readonly Network _network;
        private readonly string _cryptoCode;
        const string XPUB = "tpubDDnkuEUFyDKpk5QMsaFhohhbi2M6NauretBpcQSSchKN5H55iZc4tvi4GA8C8TCc9CPnpvfwC8U7pudc2H5DN91P7kyERt2akF1U5gdB2QQ";
        private readonly HDFingerprint _masterFingerprint = new HDFingerprint(Encoders.Hex.DecodeData("fc015d69"));

        public NodeExplorerClient(HttpClient httpClient)
        {
            _network = Network.TestNet;
            _cryptoCode = _network == Network.Main ? "BTC" : "tBTC";
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _explorerBaseUrl = _httpClient.BaseAddress?.ToString() ?? throw new ArgumentException("HttpClient must have a BaseAddress configured.");
        }

        public async Task<NodeExplorerBalanceInfo> AddressBalanceInfoAsync(string address, string currencyCode)
        {
            if (string.IsNullOrEmpty(address)) throw new ArgumentNullException(nameof(address));
            if (string.IsNullOrEmpty(currencyCode)) throw new ArgumentNullException(nameof(currencyCode));

            var requestUri = $"{_explorerBaseUrl}/v1/cryptos/{currencyCode}/addresses/{address}/balance";
            var response = await _httpClient.GetAsync(requestUri);
            var responseBody = await response.Content.ReadAsStringAsync();
            if (!response.IsSuccessStatusCode)
            {
                var ex = new Exception($"Failed requesting address balance from Node Explorer at {requestUri}. Node Explorer response status: {response.StatusCode}, Body: {responseBody}");
                Loggers.GetIntance().NodeExplorer.Error(ex.Message, ex);
                ErrorsSender.Send(ex, $"{nameof(AddressBalanceAsync)} deposit creation", ex.Message);
                throw ex;
            }
            return JsonSerializer.Deserialize<NodeExplorerBalanceInfo>(responseBody);
        }

        public async Task<decimal> AddressBalanceAsync(string address, string currencyCode)
        {
            var balanceInfo = await AddressBalanceInfoAsync(address, currencyCode);

            long availableSatoshis = balanceInfo?.Available ?? 0;

            decimal balanceInBtc = availableSatoshis / SatsPerBtc;
            return balanceInBtc;
        }

        public async Task<NodeUtxoResponse> GetUTXOsAsync(string descriptor)
        {
            try
            {
                var requestUri = $"/v1/cryptos/{_cryptoCode}/derivations/{descriptor}/utxos";
                var response = await _httpClient.GetAsync(requestUri);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    var ex = new Exception($"Failed requesting UTXOs from Node Explorer at {requestUri}. Node Explorer response status: {response.StatusCode}, Body: {responseBody}");
                    Loggers.GetIntance().NodeExplorer.Error(ex.Message, ex);
                    ErrorsSender.Send(ex, $"{nameof(GetUTXOsAsync)} UTXO retrieval", ex.Message);
                    throw ex;
                }

                return JsonSerializer.Deserialize<NodeUtxoResponse>(responseBody);
            }
            catch (Exception ex)
            {
                var errorMessage = $"Error fetching UTXOs for descriptor {descriptor}: {ex.Message}";
                Loggers.GetIntance().NodeExplorer.Error(errorMessage, ex);
                ErrorsSender.Send(ex, $"{nameof(GetUTXOsAsync)} UTXO retrieval", errorMessage);
                throw;
            }
        }

        public async Task<FeeRate> GetFeeRateAsync()
        {
            try
            {
                var requestUri = $"/v1/cryptos/{_cryptoCode}/fees/6";
                var response = await _httpClient.GetAsync(requestUri);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    var ex = new Exception($"Failed requesting fee rate from Node Explorer at {requestUri}. Node Explorer response status: {response.StatusCode}, Body: {responseBody}");
                    Loggers.GetIntance().NodeExplorer.Error(ex.Message, ex);
                    ErrorsSender.Send(ex, $"{nameof(GetFeeRateAsync)} fee rate retrieval", ex.Message);
                    throw ex;
                }

                var jsonDoc = JsonDocument.Parse(responseBody);
                var satPerKbDecimal = jsonDoc.RootElement.GetProperty("feeRate").GetDecimal();
                return new FeeRate(Money.Satoshis(satPerKbDecimal));
            }
            catch (Exception ex)
            {
                var errorMessage = $"Error fetching fee rate: {ex.Message}";
                Loggers.GetIntance().NodeExplorer.Error(errorMessage, ex);
                ErrorsSender.Send(ex, $"{nameof(GetFeeRateAsync)} fee rate retrieval", errorMessage);
                throw;
            }
        }

        public async Task<BroadcastResult> BroadcastTxAsync(Transaction tx)
        {
            var payload = new StringContent($"\"{tx.ToHex()}\"", Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync($"/v1/cryptos/{_cryptoCode}/transactions", payload);
            response.EnsureSuccessStatusCode();
            var jsonString = await response.Content.ReadAsStringAsync();
            var jsonDoc = JsonDocument.Parse(jsonString);
            return new BroadcastResult { Success = jsonDoc.RootElement.GetProperty("success").GetBoolean() };
        }

        public async Task<PSBT> GeneratePSBTAsync(string recipientAddressStr, Money amountToSend)
        {
            try
            {
                var recipientAddress = BitcoinAddress.Create(recipientAddressStr, _network);

                var extPubKey = ExtPubKey.Parse(XPUB, _network);
                var receiveDescriptor = $"wpkh({XPUB}/0/*)";
                var changeDescriptor = $"wpkh({XPUB}/1/*)";

                var utxoResponse = await GetUTXOsAsync(receiveDescriptor);
                var utxos = utxoResponse.UnspentCoins;
                var feeRate = await GetFeeRateAsync();

                if (!utxos.Any())
                {
                    throw new InvalidOperationException("No UTXOs found for this xpub. Cannot create transaction.");
                }

                var builder = _network.CreateTransactionBuilder();
                var coins = utxos.Select(u => u.AsCoin()).ToList();
                builder.AddCoins(coins);
                builder.Send(recipientAddress, amountToSend);
                builder.SendEstimatedFees(feeRate);

                var changeKey = extPubKey.Derive(1).Derive(0);
                var changeScriptPubKey = changeKey.PubKey.WitHash.ScriptPubKey;
                builder.SetChange(changeScriptPubKey);

                var tx = builder.BuildTransaction(sign: false);
                var spentCoins = builder.FindSpentCoins(tx);
                var totalInput = spentCoins.Sum(c => ((Money)c.Amount).Satoshi);
                var totalOutput = tx.Outputs.Sum(o => o.Value.Satoshi);
                var fee = Money.Satoshis(totalInput - totalOutput);

                if (!builder.Verify(tx, out var errors))
                {
                    var errorMessage = string.Join(Environment.NewLine, errors.Select(e => e.ToString()));
                    throw new InvalidOperationException($"Could not build transaction. Errors: {errorMessage}");
                }

                var psbt = PSBT.FromTransaction(tx, _network);
                psbt.AddCoins(spentCoins);

                var utxoDict = utxos.ToDictionary(u => u.OutPoint);
                for (var i = 0; i < psbt.Inputs.Count; i++)
                {
                    var input = psbt.Inputs[i];
                    if (utxoDict.TryGetValue(input.PrevOut, out var utxo))
                    {
                        try
                        {
                            var keyPathParts = utxo.KeyPath.Split('/');
                            if (keyPathParts.Length >= 2 && uint.TryParse(keyPathParts[1], out var addressIndex))
                            {
                                var derivedKey = extPubKey.Derive(0).Derive(addressIndex);
                                var keyPath = new KeyPath($"0/{addressIndex}");
                                var rootedKeyPath = new RootedKeyPath(_masterFingerprint, keyPath);
                                input.HDKeyPaths.Add(derivedKey.PubKey, rootedKeyPath);
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Warning: Could not add HD key path for input {i}: {ex.Message}");
                        }
                    }
                }

                var changeOutput = tx.Outputs.FirstOrDefault(o => o.ScriptPubKey == changeScriptPubKey);
                if (changeOutput != null)
                {
                    var changeIndex = tx.Outputs.IndexOf(changeOutput);
                    var changeKeyPath = new KeyPath("1/0");
                    var rootedKeyPath = new RootedKeyPath(_masterFingerprint, changeKeyPath);
                    psbt.Outputs[changeIndex].HDKeyPaths.Add(changeKey.PubKey, rootedKeyPath);
                }

                return psbt;
            }
            catch (Exception ex)
            {
                var errorMessage = $"Failed to generate PSBT: {ex.Message}";
                Loggers.GetIntance().NodeExplorer.Error(errorMessage, ex);
                ErrorsSender.Send(ex, $"{nameof(GeneratePSBTAsync)} PSBT generation", errorMessage);
                throw;
            }
        }

        public async Task<PSBT> GenerateMultiOutputPSBTAsync(List<(string address, Money amount)> recipients)
        {
            try
            {
                if (recipients == null || recipients.Count == 0)
                    throw new ArgumentException("Recipients list cannot be null or empty");

                var extPubKey = ExtPubKey.Parse(XPUB, _network);
                var receiveDescriptor = $"wpkh({XPUB}/0/*)";
                var changeDescriptor = $"wpkh({XPUB}/1/*)";

                var utxoResponse = await GetUTXOsAsync(receiveDescriptor);
                var utxos = utxoResponse.UnspentCoins;
                var feeRate = await GetFeeRateAsync();

                if (!utxos.Any())
                {
                    throw new InvalidOperationException("No UTXOs found for this xpub. Cannot create transaction.");
                }

                var builder = _network.CreateTransactionBuilder();
                var coins = utxos.Select(u => u.AsCoin()).ToList();
                builder.AddCoins(coins);

                foreach (var recipient in recipients)
                {
                    var recipientAddress = BitcoinAddress.Create(recipient.address, _network);
                    builder.Send(recipientAddress, recipient.amount);
                }

                builder.SendEstimatedFees(feeRate);

                var changeKey = extPubKey.Derive(1).Derive(0);
                var changeScriptPubKey = changeKey.PubKey.WitHash.ScriptPubKey;
                builder.SetChange(changeScriptPubKey);

                var tx = builder.BuildTransaction(sign: false);
                var spentCoins = builder.FindSpentCoins(tx);
                var totalInput = spentCoins.Sum(c => ((Money)c.Amount).Satoshi);
                var totalOutput = tx.Outputs.Sum(o => o.Value.Satoshi);
                var fee = Money.Satoshis(totalInput - totalOutput);

                if (!builder.Verify(tx, out var errors))
                {
                    var errorMessage = string.Join(Environment.NewLine, errors.Select(e => e.ToString()));
                    throw new InvalidOperationException($"Could not build transaction. Errors: {errorMessage}");
                }

                var psbt = PSBT.FromTransaction(tx, _network);
                psbt.AddCoins(spentCoins);

                var utxoDict = utxos.ToDictionary(u => u.OutPoint);
                for (var i = 0; i < psbt.Inputs.Count; i++)
                {
                    var input = psbt.Inputs[i];
                    if (utxoDict.TryGetValue(input.PrevOut, out var utxo))
                    {
                        try
                        {
                            var keyPathParts = utxo.KeyPath.Split('/');
                            if (keyPathParts.Length >= 2 && uint.TryParse(keyPathParts[1], out var addressIndex))
                            {
                                var derivedKey = extPubKey.Derive(0).Derive(addressIndex);
                                var keyPath = new KeyPath($"0/{addressIndex}");
                                var rootedKeyPath = new RootedKeyPath(_masterFingerprint, keyPath);
                                input.HDKeyPaths.Add(derivedKey.PubKey, rootedKeyPath);
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Warning: Could not add HD key path for input {i}: {ex.Message}");
                        }
                    }
                }

                var changeOutput = tx.Outputs.FirstOrDefault(o => o.ScriptPubKey == changeScriptPubKey);
                if (changeOutput != null)
                {
                    var changeIndex = tx.Outputs.IndexOf(changeOutput);
                    var changeKeyPath = new KeyPath("1/0");
                    var rootedKeyPath = new RootedKeyPath(_masterFingerprint, changeKeyPath);
                    psbt.Outputs[changeIndex].HDKeyPaths.Add(changeKey.PubKey, rootedKeyPath);
                }

                return psbt;
            }
            catch (Exception ex)
            {
                var errorMessage = $"Failed to generate multi-output PSBT: {ex.Message}";
                Loggers.GetIntance().NodeExplorer.Error(errorMessage, ex);
                ErrorsSender.Send(ex, $"{nameof(GenerateMultiOutputPSBTAsync)} PSBT generation", errorMessage);
                throw;
            }
        }

        public static void SavePsbtToFile(PSBT psbt, string filePath)
        {
            File.WriteAllBytes(filePath, psbt.ToBytes());
        }
    }

    public class BroadcastResult
    {
        public bool Success { get; set; }
    }

    public class NodeExplorerBalanceInfo
    {
        [JsonPropertyName("confirmed")]
        public long Confirmed { get; set; }

        [JsonPropertyName("unconfirmed")]
        public long Unconfirmed { get; set; }

        [JsonPropertyName("available")]
        public long Available { get; set; }

        [JsonPropertyName("total")]
        public long Total { get; set; }
    }
    public class NodeUtxoResponse
    {
        [JsonPropertyName("confirmed")] public UtxoSet Confirmed { get; set; }
        [JsonPropertyName("unconfirmed")] public UtxoSet Unconfirmed { get; set; }
        [JsonPropertyName("unspentCoins")] public List<NodeUnspentCoin> UnspentCoins { get; set; }
    }

    public class UtxoSet
    {
        [JsonPropertyName("utxos")] public List<NodeUnspentCoin> Utxos { get; set; }
        [JsonPropertyName("totalBalance")] public string TotalBalance { get; set; }
    }

    public class NodeUnspentCoin
    {
        [JsonPropertyName("keyPath")] public string KeyPath { get; set; }
        [JsonPropertyName("transactionId")] public string TransactionId { get; set; }
        [JsonPropertyName("index")] public uint Index { get; set; }
        [JsonPropertyName("value")] public string Value { get; set; }
        [JsonPropertyName("scriptPubKey")] public string ScriptPubKeyHex { get; set; }

        [JsonIgnore] public OutPoint OutPoint => new OutPoint(uint256.Parse(TransactionId), Index);
        [JsonIgnore] public Money Amount => Money.Parse(Value);
        [JsonIgnore] public NBitcoin.Script ScriptPubKey => NBitcoin.Script.FromHex(ScriptPubKeyHex);

        public Coin AsCoin() => new Coin(OutPoint, new TxOut(Amount, ScriptPubKey));
    }
}
