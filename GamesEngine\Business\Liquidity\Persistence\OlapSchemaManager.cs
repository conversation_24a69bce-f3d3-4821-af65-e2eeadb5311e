﻿using System;
using System.Collections.Generic;
using static GamesEngine.Business.Liquidity.Persistence.OlapTableDefinitions;

namespace GamesEngine.Business.Liquidity.Persistence
{
    public class OlapSchemaManager : OlapServiceBase, IOlapSchemaManager
    {
        public OlapSchemaManager(IDbConnectionFactory connectionFactory) : base(connectionFactory) { }

        public void CreateTablesIfNotExists(string kind)
        {
            string depositTableName = DynamicTableName(DepositTableName, kind);
            string createDepositTableSql = $@"
                CREATE TABLE IF NOT EXISTS {depositTableName} (
                    `Id` Int64,
                    `DocumentNumber` String,
                    `Amount` Decimal(16, 8),
                    `Date` DateTime64(3),
                    `Store` UInt8,
                    `AccountNumber` String,
                    `DomainId` Int32,
                    `Address` String,
                    `Created` DateTime64(3),
                    `Rate` Decimal(18, 8)
                ) ENGINE = MergeTree()
                PARTITION BY toYYYYMM(Date)
                ORDER BY (Date, Id)
                SETTINGS index_granularity = 8192;";
            ExecuteNonQuery(createDepositTableSql);

            string jarTableName = DynamicTableName(JarTableName, kind);
            string createJarTableSql = $@"
                CREATE TABLE IF NOT EXISTS {jarTableName} (
                    `Version` Int64,
                    `OriginJarId` Nullable(Int64),
                    `Description` Nullable(String),
                    `Created` DateTime64(3)
                ) ENGINE = MergeTree()
                ORDER BY (Version, Created)
                SETTINGS index_granularity = 8192;";
            ExecuteNonQuery(createJarTableSql);

            string tankTableName = DynamicTableName(TankTableName, kind);
            string createTankTableSql = $@"
                CREATE TABLE IF NOT EXISTS {tankTableName} (
                    `Id` Int64,
                    `Name` String,
                    `Description` Nullable(String),
                    `Created` DateTime64(3),
                    `OriginType` Nullable(String),
                    `OriginId` Nullable(Int64),
                    `Version` Int32
                ) ENGINE = MergeTree()
                ORDER BY Id
                SETTINGS index_granularity = 8192;";
            ExecuteNonQuery(createTankTableSql);

            string tankerTableName = DynamicTableName(TankerTableName, kind);
            string createTankerTableSql = $@"
                CREATE TABLE IF NOT EXISTS {tankerTableName} (
                    `Id` Int64,
                    `Name` String,
                    `Description` Nullable(String),
                    `Created` DateTime64(3),
                    `Version` Int32
                ) ENGINE = MergeTree()
                ORDER BY Id
                SETTINGS index_granularity = 8192;";
            ExecuteNonQuery(createTankerTableSql);

            string jarDetailTableName = DynamicTableName(JarDetailTableName, kind);
            string createJarDetailTableSql = $@"
                CREATE TABLE IF NOT EXISTS {jarDetailTableName} (
                    `JarVersion` Int64,
                    `DepositId` Int64,
                    `Created` DateTime64(3)
                ) ENGINE = MergeTree()
                ORDER BY (JarVersion, DepositId)
                SETTINGS index_granularity = 8192;";
            ExecuteNonQuery(createJarDetailTableSql);

            string tankDetailTableName = DynamicTableName(TankDetailTableName, kind);
            string createTankDetailTableSql = $@"
                CREATE TABLE IF NOT EXISTS {tankDetailTableName} (
                    `TankId` Int64,
                    `DepositId` Int64,
                    `TankVersion` Int32,
                    `Created` DateTime64(3)
                ) ENGINE = MergeTree()
                ORDER BY (TankId, TankVersion, DepositId)
                SETTINGS index_granularity = 8192;";
            ExecuteNonQuery(createTankDetailTableSql);

            string tankerDetailTableName = DynamicTableName(TankerDetailTableName, kind);
            string createTankerDetailTableSql = $@"
                CREATE TABLE IF NOT EXISTS {tankerDetailTableName} (
                    `TankerId` Int64,
                    `DepositId` Int64,
                    `TankerVersion` Int32,
                    `Created` DateTime64(3)
                ) ENGINE = MergeTree()
                ORDER BY (TankerId, TankerVersion, DepositId)
                SETTINGS index_granularity = 8192;";
            ExecuteNonQuery(createTankerDetailTableSql);

            string withdrawalTableName = DynamicTableName(WithdrawalTableName, kind);
            string createWithdrawalTableSql = $@"
                CREATE TABLE IF NOT EXISTS {withdrawalTableName} (
                    `Id` Int64,
                    `DocumentNumber` String,
                    `Amount` Decimal(16, 8),
                    `Date` DateTime64(3),
                    `Store` UInt8,
                    `AccountNumber` String,
                    `DomainId` Int32,
                    `Address` String,
                    `Created` DateTime64(3),
                    `Rate` Decimal(18, 8)
                ) ENGINE = MergeTree()
                PARTITION BY toYYYYMM(Date)
                ORDER BY (Date, Id)
                SETTINGS index_granularity = 8192;";
            ExecuteNonQuery(createWithdrawalTableSql);

            string bottleTableName = DynamicTableName(BottleTableName, kind);
            string createBottleTableSql = $@"
                CREATE TABLE IF NOT EXISTS {bottleTableName} (
                    `Id` Int64,
                    `Description` Nullable(String),
                    `Created` DateTime64(3)
                ) ENGINE = MergeTree()
                ORDER BY Id
                SETTINGS index_granularity = 8192;";
            ExecuteNonQuery(createBottleTableSql);

            string dispenserTableName = DynamicTableName(DispenserTableName, kind);
            string createDispenserTableSql = $@"
                CREATE TABLE IF NOT EXISTS {dispenserTableName} (
                    `Id` Int64,
                    `Description` Nullable(String),
                    `Created` DateTime64(3),
                    `Version` Int32
                ) ENGINE = MergeTree()
                ORDER BY Id
                SETTINGS index_granularity = 8192;";
            ExecuteNonQuery(createDispenserTableSql);

            string bottleDetailTableName = DynamicTableName(BottleDetailTableName, kind);
            string createBottleDetailTableSql = $@"
                CREATE TABLE IF NOT EXISTS {bottleDetailTableName} (
                    `WithdrawalId` Int64,
                    `BottleId` Int64,
                    `Created` DateTime64(3)
                ) ENGINE = MergeTree()
                ORDER BY (WithdrawalId, BottleId)
                SETTINGS index_granularity = 8192;";
            ExecuteNonQuery(createBottleDetailTableSql);

            string dispenserDetailTableName = DynamicTableName(DispenserDetailTableName, kind);
            string createDispenserDetailTableSql = $@"
                CREATE TABLE IF NOT EXISTS {dispenserDetailTableName} (
                    `WithdrawalId` Int64,
                    `DispenserId` Int64,
                    `Created` DateTime64(3),
                    `DispenserVersion` Int32
                ) ENGINE = MergeTree()
                ORDER BY (DispenserId, DispenserVersion, WithdrawalId)
                SETTINGS index_granularity = 8192;";
            ExecuteNonQuery(createDispenserDetailTableSql);

            CreateDepositCurrentJarAssignmentTableAndMv(kind, jarDetailTableName, depositTableName);
            CreateDepositCurrentTankAssignmentTableAndMv(kind, tankDetailTableName, depositTableName);
            CreateDepositCurrentTankerAssignmentTableAndMv(kind, tankerDetailTableName, depositTableName);
            CreateWithdrawalCurrentDispenserAssignmentTableAndMv(kind, dispenserDetailTableName, withdrawalTableName);

            string dailySummaryTableName = DynamicTableName(DailyTransactionSummaryTable, kind);
            string createDailySummaryTableSql = $@"
                CREATE TABLE IF NOT EXISTS {dailySummaryTableName} (
                    `TransactionDate` Date,
                    `DomainId` Int32,
                    `TotalDeposits_state` AggregateFunction(sum, Decimal(16,8)),
                    `TotalWithdrawals_state` AggregateFunction(sum, Decimal(16,8)),
                    `DepositsCount_state` AggregateFunction(count),
                    `WithdrawalsCount_state` AggregateFunction(count)
                ) ENGINE = AggregatingMergeTree()
                PARTITION BY toYYYYMM(TransactionDate)
                ORDER BY (TransactionDate, DomainId)
                SETTINGS index_granularity = 8192;";
            ExecuteNonQuery(createDailySummaryTableSql);

            string mvDailyDepositName = DynamicTableName(MvDailyDepositToSummary, kind);
            string createMvDailyDepositSql = $@"
                CREATE MATERIALIZED VIEW IF NOT EXISTS {mvDailyDepositName} TO {dailySummaryTableName}
                AS SELECT
                    toDate(Date) AS TransactionDate,
                    DomainId,
                    sumState(Amount) AS TotalDeposits_state,
                    sumState(CAST(0 AS Decimal(16,8))) AS TotalWithdrawals_state,
                    countState() AS DepositsCount_state,
                    countStateIf(Amount, 0) AS WithdrawalsCount_state
                FROM {depositTableName}
                GROUP BY toDate(Date), DomainId;";
            ExecuteNonQuery(createMvDailyDepositSql);

            string mvDailyWithdrawalName = DynamicTableName(MvDailyWithdrawalToSummary, kind);
            string createMvDailyWithdrawalSql = $@"
                CREATE MATERIALIZED VIEW IF NOT EXISTS {mvDailyWithdrawalName} TO {dailySummaryTableName}
                AS SELECT
                    toDate(Date) AS TransactionDate,
                    DomainId,
                    sumState(CAST(0 AS Decimal(16,8))) AS TotalDeposits_state,
                    sumState(Amount) AS TotalWithdrawals_state,
                    countStateIf(Amount, 0) AS DepositsCount_state,
                    countState() AS WithdrawalsCount_state
                FROM {withdrawalTableName}
                GROUP BY toDate(Date), DomainId;";
            ExecuteNonQuery(createMvDailyWithdrawalSql);
        }

        private void CreateDepositCurrentJarAssignmentTableAndMv(string kind, string jarDetailTableName, string depositTableName)
        {
            string assignmentTableName = DynamicTableName(DepositCurrentJarAssignmentTable, kind);
            string mvName = DynamicTableName(MvDepositCurrentJarAssignment, kind);

            string createAssignmentTableSql = $@"
                CREATE TABLE IF NOT EXISTS {assignmentTableName} (
                    `DepositId` Int64,
                    `CurrentJarVersion_state` AggregateFunction(argMax, Int64, DateTime64(3)),
                    `Amount_state` AggregateFunction(any, Decimal(16, 8))
                ) ENGINE = AggregatingMergeTree()
                PRIMARY KEY (DepositId)
                ORDER BY (DepositId);";
            ExecuteNonQuery(createAssignmentTableSql);

            string createMvSql = $@"
                CREATE MATERIALIZED VIEW IF NOT EXISTS {mvName} TO {assignmentTableName}
                AS SELECT
                    jd.DepositId,
                    argMaxState(jd.JarVersion, jd.Created) AS CurrentJarVersion_state,
                    anyState(d.Amount) AS Amount_state
                FROM {jarDetailTableName} jd
                JOIN {depositTableName} d ON jd.DepositId = d.Id
                GROUP BY jd.DepositId;";
            ExecuteNonQuery(createMvSql);
        }

        private void CreateDepositCurrentTankAssignmentTableAndMv(string kind, string tankDetailTableName, string depositTableName)
        {
            string assignmentTableName = DynamicTableName(DepositCurrentTankAssignmentTable, kind);
            string mvName = DynamicTableName(MvDepositCurrentTankAssignment, kind);

            string createAssignmentTableSql = $@"
                CREATE TABLE IF NOT EXISTS {assignmentTableName} (
                    `DepositId` Int64,
                    `CurrentTankId_state` AggregateFunction(argMax, Int64, DateTime64(3)),
                    `Amount_state` AggregateFunction(any, Decimal(16, 8))
                ) ENGINE = AggregatingMergeTree()
                PRIMARY KEY (DepositId)
                ORDER BY (DepositId);";
            ExecuteNonQuery(createAssignmentTableSql);

            string createMvSql = $@"
                CREATE MATERIALIZED VIEW IF NOT EXISTS {mvName} TO {assignmentTableName}
                AS SELECT
                    td.DepositId,
                    argMaxState(td.TankId, td.Created) AS CurrentTankId_state,
                    anyState(d.Amount) AS Amount_state
                FROM {tankDetailTableName} td
                JOIN {depositTableName} d ON td.DepositId = d.Id
                GROUP BY td.DepositId;";
            ExecuteNonQuery(createMvSql);
        }

        private void CreateDepositCurrentTankerAssignmentTableAndMv(string kind, string tankerDetailTableName, string depositTableName)
        {
            string assignmentTableName = DynamicTableName(DepositCurrentTankerAssignmentTable, kind);
            string mvName = DynamicTableName(MvDepositCurrentTankerAssignment, kind);

            string createAssignmentTableSql = $@"
                CREATE TABLE IF NOT EXISTS {assignmentTableName} (
                    `DepositId` Int64,
                    `CurrentTankerId_state` AggregateFunction(argMax, Int64, DateTime64(3)),
                    `Amount_state` AggregateFunction(any, Decimal(16, 8))
                ) ENGINE = AggregatingMergeTree()
                PRIMARY KEY (DepositId)
                ORDER BY (DepositId);";
            ExecuteNonQuery(createAssignmentTableSql);

            string createMvSql = $@"
                CREATE MATERIALIZED VIEW IF NOT EXISTS {mvName} TO {assignmentTableName}
                AS SELECT
                    td.DepositId,
                    argMaxState(td.TankerId, td.Created) AS CurrentTankerId_state,
                    anyState(d.Amount) AS Amount_state
                FROM {tankerDetailTableName} td
                JOIN {depositTableName} d ON td.DepositId = d.Id
                GROUP BY td.DepositId;";
            ExecuteNonQuery(createMvSql);
        }

        private void CreateWithdrawalCurrentDispenserAssignmentTableAndMv(string kind, string dispenserDetailTableName, string withdrawalTableName)
        {
            string assignmentTableName = DynamicTableName(WithdrawalCurrentDispenserAssignmentTable, kind);
            string mvName = DynamicTableName(MvWithdrawalCurrentDispenserAssignment, kind);

            string createAssignmentTableSql = $@"
                CREATE TABLE IF NOT EXISTS {assignmentTableName} (
                    `WithdrawalId` Int64,
                    `CurrentDispenserId_state` AggregateFunction(argMax, Int64, DateTime64(3)),
                    `Amount_state` AggregateFunction(any, Decimal(16, 8))
                ) ENGINE = AggregatingMergeTree()
                PRIMARY KEY (WithdrawalId)
                ORDER BY (WithdrawalId);";
            ExecuteNonQuery(createAssignmentTableSql);

            string createMvSql = $@"
                CREATE MATERIALIZED VIEW IF NOT EXISTS {mvName} TO {assignmentTableName}
                AS SELECT
                    dd.WithdrawalId,
                    argMaxState(dd.DispenserId, dd.Created) AS CurrentDispenserId_state,
                    anyState(w.Amount) AS Amount_state
                FROM {dispenserDetailTableName} dd
                JOIN {withdrawalTableName} w ON dd.WithdrawalId = w.Id
                GROUP BY dd.WithdrawalId;";
            ExecuteNonQuery(createMvSql);
        }

        public void DropAllTables(string kind)
        {
            ExecuteNonQuery($"DROP VIEW IF EXISTS {DynamicTableName(MvDailyDepositToSummary, kind)};");
            ExecuteNonQuery($"DROP VIEW IF EXISTS {DynamicTableName(MvDailyWithdrawalToSummary, kind)};");

            ExecuteNonQuery($"DROP VIEW IF EXISTS {DynamicTableName(MvDepositCurrentJarAssignment, kind)};");
            ExecuteNonQuery($"DROP VIEW IF EXISTS {DynamicTableName(MvDepositCurrentTankAssignment, kind)};");
            ExecuteNonQuery($"DROP VIEW IF EXISTS {DynamicTableName(MvDepositCurrentTankerAssignment, kind)};");
            ExecuteNonQuery($"DROP VIEW IF EXISTS {DynamicTableName(MvWithdrawalCurrentDispenserAssignment, kind)};");

            var baseTableNames = new List<string>
            {
                DepositTableName, JarTableName, TankTableName, TankerTableName, JarDetailTableName, TankDetailTableName, TankerDetailTableName,
                WithdrawalTableName, BottleTableName, DispenserTableName, BottleDetailTableName, DispenserDetailTableName,
                DepositCurrentJarAssignmentTable, DepositCurrentTankAssignmentTable, DepositCurrentTankerAssignmentTable, WithdrawalCurrentDispenserAssignmentTable, DailyTransactionSummaryTable
            };

            foreach (var baseName in baseTableNames)
            {
                string tableName = DynamicTableName(baseName, kind);
                string dropSql = $"DROP TABLE IF EXISTS {tableName};";
                ExecuteNonQuery(dropSql);
            }
        }
    }

}
