using log4net;
using Puppeteer.EventSourcing;

namespace LiquidityAPI
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var logRepository = log4net.LogManager.GetRepository(System.Reflection.Assembly.GetEntryAssembly());
            log4net.Config.XmlConfigurator.Configure(logRepository, new System.IO.FileInfo("log4net.config"));

            Loggers.GetIntance().LoggerDirectory = "/tmp/liquidityapi";
            Loggers.GetIntance().Drivers.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "drivers-execute"));

            Loggers.GetIntance().Db.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "database"));
            Loggers.GetIntance().Emails.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "emails"));
            Loggers.GetIntance().Sentinel.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "sentinel"));
            Loggers.GetIntance().NodeExplorer.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "nodeexplorer"));

            CreateHostBuilder(args).Build().Run();
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>().
                    UseUrls("http://0.0.0.0:5060").
                    ConfigureLogging((hostingContext, logging) =>
                    {
                        // Requires `using Microsoft.Extensions.Logging;`
                        logging.AddConfiguration(hostingContext.Configuration.GetSection("Logging"));
                        logging.AddConsole();
                        logging.AddDebug();
                        logging.AddEventSourceLogger();
                    });
                });
    }
}