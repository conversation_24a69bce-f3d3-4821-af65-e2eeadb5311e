﻿using GamesEngine.Business.Liquidity.Containers;
using GamesEngine.Business.Liquidity.Transactions;
using GamesEngine.Domains;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Business.Liquidity.Containers.Jar;
using static GamesEngine.Business.Liquidity.Containers.Tank;
using static GamesEngine.Business.Liquidity.Containers.Tanker;

namespace GamesEngine.Business.Liquidity
{
    internal class Source : Objeto
    {
        private readonly HashSet<EnclosureDeposit> wholeEnclosurDeposits = new HashSet<EnclosureDeposit>();
        private readonly List<Xpub> _xpubs = new List<Xpub>();
        private readonly Dictionary<int, Tanker> _tankers = new Dictionary<int, Tanker>();
        private readonly Dictionary<int, Tanker> obsoleteTankers = new Dictionary<int, Tanker>();
        private const int MAXLRUCONTAINER = 8;
        private readonly Dictionary<int, Tank> _tanks = new Dictionary<int, Tank>();
        private readonly Dictionary<int, Tank> obsoleteTanks = new Dictionary<int, Tank>();
        private readonly SlidingWindow<Transaction> transactionVelocityWindow;
        private readonly LRUCache<int,Tank> _lruTanks;
        private readonly LRUCache<int,Tanker> _lruTankers;

        private DateTime minDateTanker;
        private DateTime maxDateTanker;
        private DateTime minDateTank;
        private DateTime maxDateTank;
        internal Source(Liquid liquid)
        {
            if (liquid == null) throw new ArgumentNullException(nameof(liquid));

            int vesion = NextJarVersion();
            transactionVelocityWindow = new SlidingWindow<Transaction>(
                TimeSpan.FromHours(1),
                transaction => transaction.CreatedAt);
            _lruTanks = new LRUCache<int, Tank>(MAXLRUCONTAINER);
            _lruTankers = new LRUCache<int, Tanker>(MAXLRUCONTAINER);
            Jar = new Jar(this, liquid.Kind,liquid, vesion);
            CurrentVesion = vesion;
            Liquid = liquid;
        }

        internal string Kind => Liquid.Kind;
        internal decimal Amount { get; set; }        
        internal Jar Jar { get; private set; }
        internal Liquid Liquid { get; private set; }
        internal IEnumerable<Tank> Tanks => _tanks.Values;
        internal IEnumerable<Tank> ObsoleteTanks => obsoleteTanks.Values;
        internal IEnumerable<Tanker> Tankers => _tankers.Values;
        internal IEnumerable<Tanker> ObsoleteTankers => obsoleteTankers.Values;

        internal DateTime MinDateTank
        {
            get
            {
                if(minDateTank == DateTime.MinValue)
                    minDateTank = CalculateMinDateTank();
                return minDateTank;
            }
        }
        internal DateTime MaxDateTank
        {
            get
            {
                if(maxDateTank == DateTime.MinValue)
                    maxDateTank = CalculateMaxDateTank();
                return maxDateTank;
            }
        }

        internal DateTime MinDateTanker
        {
            get
            {
                if (minDateTanker == DateTime.MinValue)
                    minDateTanker = CalculateMinDateTankers();
                return minDateTanker;
            }
        }
        internal DateTime MaxDateTanker
        {
            get
            {
                if (maxDateTanker == DateTime.MinValue)
                    maxDateTanker = CalculateMaxDateTankers();
                return maxDateTanker;
            }
        }

        internal IEnumerable<Tank> RootHierarchyTanksAndDescendants()
        {
            return OrderTankByCreated(Tanks.Where(c => c is TankReady));
        }


        internal IEnumerable<Tank> AvailableParentTanks(FilterTankStatus status)
        {
            return FilterTankByStatus(status);
        }

        internal IEnumerable<Tank> FilteredParentTanks(FilterTankStatus status, string name = null, string color = null)
        {
            var filteredTanks = FilterTankByStatus(status);
            if (!string.IsNullOrWhiteSpace(name))
            {
                filteredTanks = filteredTanks.Where(t => t.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
            }
            if (!string.IsNullOrWhiteSpace(color))
            {
                filteredTanks = filteredTanks.Where(t => t.ContainerColor.Equals(color, StringComparison.OrdinalIgnoreCase));
            }

            return filteredTanks;
        }

        internal IEnumerable<Tanker> FilterTankersByStatus(TankerStatus status)
        {
            switch (status)
            {
                case TankerStatus.PENDING:
                    return OrderTankerByCreated(Tankers.Where(c => c is TankerPending));
                case TankerStatus.SEALED:
                    return OrderTankerByCreated(Tankers.Where(c => c is TankerSealed));
                case TankerStatus.DISPATCHED:
                    return OrderTankerByCreated(Tankers.Where(c => c is TankerDispatched));
                case TankerStatus.ARCHIVED:
                    return OrderTankerByCreated(Tankers.Where(c => c is TankerArchived));
                default:
                    return OrderTankerByCreated(Tankers.Where(c => c is TankerPending));
            }
        }

        internal IEnumerable<Tanker> FilterTankersBy(TankerStatus status,string name = null, string color = null)
        {
           var filteredTankers = FilterTankersByStatus(status);
            if (!string.IsNullOrWhiteSpace(name))
            {
                filteredTankers = filteredTankers.Where(t => t.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
            }
            if (!string.IsNullOrWhiteSpace(color))
            {
                filteredTankers = filteredTankers.Where(t => t.ContainerColor.Equals(color, StringComparison.OrdinalIgnoreCase));
            }
            return filteredTankers;
        }

        private IEnumerable<Tanker> OrderTankerByCreated(IEnumerable<Tanker> tankers)
        {
            if (tankers == null) throw new ArgumentNullException(nameof(tankers));
            return tankers.OrderByDescending(t => t.CreatedAt);
        }

        private IEnumerable<Tank> OrderTankByCreated(IEnumerable<Tank> tanks)
        {
            if (tanks == null) throw new ArgumentNullException(nameof(tanks));
            return tanks.OrderByDescending(t => t.CreatedAt);
        }

        internal IEnumerable<Tank> FilterTankByStatus(FilterTankStatus status)
        {
            switch (status)
            {
                case FilterTankStatus.READY:
                    return OrderTankByCreated(Tanks.Where(c =>  c.Parent == null && c is TankReady));
                case FilterTankStatus.LOCKED:
                    return OrderTankByCreated(Tanks.Where(c => c.Parent == null && c is TankLocked));
                case FilterTankStatus.DISPATCHED:
                    return OrderTankByCreated(Tanks.Where(c => c.Parent == null && c is TankDispatched));
                case FilterTankStatus.ARCHIVED:
                    return OrderTankByCreated(Tanks.Where(c => c.Parent == null && c is TankArchived));
                case FilterTankStatus.DISCARDED:
                    return OrderTankByCreated(Tanks.Where(c => c.Parent == null && c is TankDiscarded));
                default:
                    return OrderTankByCreated(Tanks.Where(c => c.Parent == null && c is TankReady));
            }
        }

        internal IEnumerable<EnclosureDeposit> FindDepositEnclosuresFor(string externalAtAddress, DateTime filterDate)
        {
            if (string.IsNullOrWhiteSpace(externalAtAddress)) throw new ArgumentNullException(nameof(externalAtAddress));
            if (filterDate == DateTime.MinValue) throw new ArgumentNullException(nameof(filterDate));

            return wholeEnclosurDeposits.Where(enclosure => enclosure.Deposit.ExternalAtAddress == externalAtAddress && enclosure.ConfirmedDate.Date == filterDate.Date).OrderByDescending(enclosure => enclosure.ConfirmedDate);
        }

        internal void DetachEnclosureDeposit(EnclosureDeposit enclosureDeposit)
        {
            if (enclosureDeposit == null) throw new ArgumentNullException(nameof(enclosureDeposit));
            wholeEnclosurDeposits.Remove(enclosureDeposit);
        }

        internal bool ExsitXpub(string xpub)
        {
            if (xpub == null) throw new ArgumentNullException(nameof(xpub));
            return _xpubs.Any(s => s.Value == xpub);
        }

        internal void AddXpub(Xpub xpub)
        {
            if (xpub == null) throw new ArgumentNullException(nameof(xpub));

            _xpubs.Add(xpub);
        }

        internal IEnumerable<Xpub> Xpubs => _xpubs.AsReadOnly();

        private string GenerateNewAddress()
        {
            if (!_xpubs.Any())
                throw new InvalidOperationException("No XPUBs available to derive addresses.");

            // Ejemplo sencillo: usar el primer XPUB disponible para generar una dirección
            return _xpubs.First().GenerateAddress();
        }

        internal Deposit CreateDraftDeposit(bool itIsThePresent, DateTime createdAt, int depositId, string invoiceId, int authorizationId, int externalReference, string destination, decimal exchangeAmount, decimal exchangeRate, string confirmedCurrency, decimal confirmedAmount, int totalConfirmations, int storeId, Domain domain)
        {
            return CreateDraftDeposit(
                itIsThePresent,
                createdAt,
                depositId,
                invoiceId,
                authorizationId,
                string.Empty, // externalAtAddress
                externalReference,
                destination,
                exchangeAmount,
                exchangeRate,
                confirmedCurrency,
                confirmedAmount,
                totalConfirmations,
                storeId,
                domain
            );
        }

        internal Deposit CreateDraftDeposit(bool itIsThePresent, DateTime createdAt, int depositId, string invoiceId, int authorizationId, string externalAtAddress, int externalReference, string destination, decimal exchangeAmount, decimal exchangeRate, string confirmedCurrency, decimal confirmedAmount, int totalConfirmations, int storeId, Domain domain)
        {
            if (depositId <= 0) throw new ArgumentNullException(nameof(depositId), "Deposit ID must be greater than zero.");
            if (string.IsNullOrWhiteSpace(invoiceId)) throw new ArgumentNullException(nameof(invoiceId));
            if (authorizationId <= 0) throw new ArgumentNullException(nameof(authorizationId), "Authorization ID must be greater than zero.");
            if (externalReference <= 0) throw new ArgumentNullException(nameof(externalReference), "External reference must be greater than zero.");
            if (string.IsNullOrWhiteSpace(destination)) throw new ArgumentNullException(nameof(destination), "Destination cannot be null or empty.");
            if (createdAt == DateTime.MinValue) throw new ArgumentNullException(nameof(createdAt), "Date cannot be empty.");
            if (exchangeAmount <= 0) throw new ArgumentNullException(nameof(exchangeAmount), "Exchange amount must be greater than zero.");            
            if (exchangeRate <= 0) throw new ArgumentNullException(nameof(exchangeRate), "Rate must be greater than zero.");
            if (string.IsNullOrWhiteSpace(confirmedCurrency)) throw new ArgumentNullException(nameof(confirmedCurrency), "Confirmed currency cannot be null or empty.");
            if (confirmedAmount <= 0) throw new ArgumentNullException(nameof(confirmedAmount), "Confirmed amount must be greater than zero.");
            if (totalConfirmations < 0) throw new ArgumentNullException(nameof(totalConfirmations), "Total confirmations cannot be negative.");
            if (storeId <= 0) throw new ArgumentNullException(nameof(storeId), "Store ID must be greater than zero.");
            if (domain == null) throw new ArgumentNullException(nameof(domain), "Domain cannot be null.");

            var address = GenerateNewAddress();
            var draftConfirmDeposit = Jar.CreateDraftDeposit(
                itIsThePresent, 
                createdAt, 
                address,
                depositId,
                invoiceId,
                authorizationId,
                externalAtAddress,
                externalReference,
                destination,
                exchangeAmount,
                exchangeRate, 
                confirmedCurrency, 
                confirmedAmount,
                totalConfirmations,
                storeId,
                domain
            );

            var depositEnclosure = Jar.FindEnclosureDeposit(depositId);
            wholeEnclosurDeposits.Add(depositEnclosure);

            return draftConfirmDeposit;
        }

        internal Deposit ConfirmDeposit(bool itIsThePresent, DateTime createdAt, Deposit deposit)
        {
            if (deposit == null) throw new ArgumentNullException(nameof(deposit));
            if (createdAt == DateTime.MinValue) throw new ArgumentOutOfRangeException(nameof(createdAt), "Date cannot be empty.");

            var confirmedDeposit = Jar.ConfirmDeposit(itIsThePresent, createdAt, deposit);
            transactionVelocityWindow.Add(deposit, createdAt);
            Amount += deposit.Amount;
            return confirmedDeposit;
        }

        internal Deposit CancelDeposit(bool itIsThePresent, DateTime createdAt, Deposit draftConfirmDeposit)
        {
            if (draftConfirmDeposit == null) throw new ArgumentNullException(nameof(draftConfirmDeposit));
            if (createdAt == DateTime.MinValue) throw new ArgumentOutOfRangeException(nameof(createdAt), "Date cannot be empty.");
            var canceledDeposit = Jar.CancelDeposit(itIsThePresent, createdAt, draftConfirmDeposit);
            return canceledDeposit;
        }

        private int tankConsecutive = 0;
        internal int NextTankId()
        {
            return tankConsecutive + 1;
        }

        private int tankerConsecutive = 0;
        internal int NextTankerId()
        {
            return tankerConsecutive + 1;
        }


        internal int CurrentVesion { get; private set; }
        internal int NextJarVersion()
        {
            return CurrentVesion + 1;
        }

        internal int DepositConsecutive { get; set; }
        internal int NextDepositId()
        {
            return DepositConsecutive + 1;
        }

        internal void DelegateJar(bool itIsThePresent, DateTime now, int vesion, LegacyJar previousLegacyJar, IEnumerable<EnclosureDeposit> delegatedDeposits)
        {
            if (vesion <= 0) throw new ArgumentNullException(nameof(vesion));
            if (previousLegacyJar == null) throw new ArgumentNullException(nameof(previousLegacyJar));
            if (delegatedDeposits == null) throw new ArgumentNullException(nameof(delegatedDeposits));

            if (vesion <= CurrentVesion) throw new GameEngineException($"Version {vesion} is not greater than the current version {CurrentVesion}.");

            var newJar = new Jar(this, Jar.Kind, vesion, previousLegacyJar, delegatedDeposits);
            Jar = newJar;
            CurrentVesion = vesion;

            decimal totalDelegatedAmount = delegatedDeposits.Sum(d => d.Amount);

            if (Integration.UseKafka)
            {
                using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, Integration.Kafka.TopicForContainerEvents))
                {
                    string description = $"Jar for {Kind} V{CurrentVesion}";
                    CreatedJarMessage jarMessage = new CreatedJarMessage(CurrentVesion, previousLegacyJar.Version, Jar.Kind, description, now);
                    buffer.Send(jarMessage);
                }
            }
        }

        internal bool IsTankInUse(Tank tank)
        {
            if (tank == null) throw new ArgumentNullException(nameof(tank));

            return _tankers.Values.Any(tanker => tanker.Tanks.Contains(tank));
        }

        internal bool ExistTank(int tankId)
        {
            if (tankId <= 0) throw new ArgumentNullException(nameof(tankId));

            var result = _tanks.ContainsKey(tankId);
            if (!result) result = obsoleteTanks.ContainsKey(tankId);
            return result;
        }

        internal bool IsTankObsolete(int tankId)
        {
            if (tankId <= 0) throw new ArgumentNullException(nameof(tankId));
            return obsoleteTanks.ContainsKey(tankId);
        }

        internal bool IsTankerObsolete(int tankerId)
        {
            if (tankerId <= 0) throw new ArgumentNullException(nameof(tankerId));
            return obsoleteTankers.ContainsKey(tankerId);
        }

        internal void AddOrUpdateTank(Tank result)
        {
            if (result == null) throw new ArgumentNullException(nameof(result));

            if (result is TankDiscarded tankDiscarded)
            {
                if (_tanks.ContainsKey(result.Id))
                {
                    _tanks.Remove(result.Id);
                    obsoleteTanks.Add(result.Id, tankDiscarded);
                }
                return;
            }
            else if (result is TankArchived tankArchived)
            {
                if (_tanks.ContainsKey(result.Id))
                {
                    _tanks.Remove(result.Id);
                    obsoleteTanks.Add(result.Id, tankArchived);
                }
                return;
            }
            else if (result is TankDispatched tankDispatched)
            {
                if (_tanks.ContainsKey(result.Id))
                {
                    _tanks.Remove(result.Id);
                    obsoleteTanks.Add(result.Id, tankDispatched);
                }
                return;
            }

            if (_tanks.TryGetValue(result.Id, out Tank foundTank))
            {
                _tanks[result.Id] = result;
            }
            else
            {
                _tanks.Add(result.Id, result);
                tankConsecutive = result.Id;
            }
        }

        internal void AddLRUTankContainer(TankReady tank)
        {
            if(tank == null) throw new ArgumentNullException(nameof(tank));
            _lruTanks.Put(tank.Id, tank);
        } 
        internal void AddLRUTankerContainer(TankerPending tanker)
        {
            if(tanker == null) throw new ArgumentNullException(nameof(tanker));
            _lruTankers.Put(tanker.Id, tanker);
        }

        internal IEnumerable<Tank> RecentTanks()
        {
            List<Tank> recentTanks = new List<Tank>();
            foreach(var tank in _lruTanks.GetValues())
            {
                recentTanks.Add(tank);
            }
           return recentTanks;
        }
        internal IEnumerable<Tanker> RecentTankers()
        {
            List<Tanker> recentTankers = new List<Tanker>();
            foreach (var tanker in _lruTankers.GetValues())
            {
                recentTankers.Add(tanker);
            }
            return recentTankers;
        }


        internal Tank FindTank(int tankId)
        {
            if (tankId <= 0) throw new ArgumentNullException(nameof(tankId));

            if (_tanks.TryGetValue(tankId, out Tank foundTank))
            {
                return foundTank;
            }

            if (obsoleteTanks.TryGetValue(tankId, out Tank foundObsoleteTank))
            {
                return foundObsoleteTank;
            }
            throw new GameEngineException($"Tank with id: {tankId} was not found in source kind {Kind}");
        }

        internal TankerPending CreateTanker(bool itIsThePresent, DateTime createdAt, int tankerId, string name, string description, IEnumerable<int> tankIds)
        {
            if (createdAt == DateTime.MinValue) throw new ArgumentNullException(nameof(createdAt), "Date cannot be empty.");
            if (tankerId <= 0) throw new ArgumentOutOfRangeException(nameof(tankerId), "Tanker ID must be greater than zero.");
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (tankIds == null || !tankIds.Any()) throw new ArgumentException("Tank IDs cannot be null or empty.", nameof(tankIds));

            if (tankIds.Any(tankId => !_tanks.ContainsKey(tankId))) throw new GameEngineException($"Some tanks with ids: {string.Join(", ", tankIds)} were not found in source kind {Kind}");

            List<TankReady> tankReadys = new List<TankReady>();

            foreach (var tankId in tankIds)
            {
                if (_tanks.TryGetValue(tankId, out Tank foundTank))
                {
                    if (Liquid.Source.IsTankInUse(foundTank)) throw new GameEngineException($"Tank with id: {tankId} is already in use by another tanker.");

                    if (foundTank is TankReady tankReady)
                    {
                        tankReadys.Add(tankReady);
                    }
                    else
                    {
                        throw new GameEngineException($"Tank with id: {tankId} is not ready.");
                    }
                }
            }

            var tanker = new TankerPending(tankerId, name, description, createdAt, 1, Kind, this, tankReadys);
            AddOrUpdateTanker(tanker);
            AddLRUTankerContainer(tanker);
            CreatedTankerEvent createdTankEvent = new CreatedTankerEvent(tankerId);
            PlatformMonitor.GetInstance().WhenNewEvent(createdTankEvent);

            return tanker;
        }

        internal TankerPending CreateTanker(bool itIsThePresent, DateTime createdAt, int tankerId, string name, string description)
        {
            if (createdAt == DateTime.MinValue) throw new ArgumentNullException(nameof(createdAt), "Date cannot be empty.");
            if (tankerId <= 0) throw new ArgumentOutOfRangeException(nameof(tankerId), "Tanker ID must be greater than zero.");
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            var tanker = new TankerPending(tankerId, name, description, createdAt, 1, Kind, this);
            AddOrUpdateTanker(tanker);
            AddLRUTankerContainer(tanker);
            CreatedTankerEvent createdTankEvent = new CreatedTankerEvent(tankerId);
            PlatformMonitor.GetInstance().WhenNewEvent(createdTankEvent);

            return tanker;
        }

        internal TankerPending CreateTankerWithDeposits(bool itIsThePresent, DateTime createdAt, int version, int tankerId, string name, string description, IEnumerable<int> deposits)
        {
            if (createdAt == DateTime.MinValue) throw new ArgumentNullException(nameof(createdAt), "Date cannot be empty.");
            if (tankerId <= 0) throw new ArgumentOutOfRangeException(nameof(tankerId), "Tanker ID must be greater than zero.");
            if(deposits == null) throw new ArgumentNullException(nameof(deposits));
            if (!deposits.Any()) throw new ArgumentException("Enclosure deposits cannot be empty.", nameof(deposits));
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            List<EnclosureDeposit> enclouserDeposits = new List<EnclosureDeposit>();
            foreach( var depositId in deposits)
            {
                if (depositId <= 0) throw new ArgumentNullException(nameof(depositId));
                if (!Jar.ExistDeposit(depositId)) throw new GameEngineException($"Deposit with id: {depositId} was not found in jar version {Jar.Version}.");
                if (!Jar.ExistDepositConfirmed(depositId)) throw new GameEngineException($"Deposit with id: {depositId} was not found or is not confimed in jar version {Jar.Version}.");

                enclouserDeposits.Add(Jar.FindEnclosureDeposit(depositId));
            }
            TankerPending tanker = Jar.CreateTankerWithDeposits(itIsThePresent, createdAt, version, tankerId, name, description, deposits);
            AddLRUTankerContainer(tanker);
            return tanker;

        }

        internal void MoveToTanker(bool itIsThePresent, DateTime createdAt, int tankerId, List<int> tankIds)
        {
            if (createdAt == DateTime.MinValue) throw new ArgumentNullException(nameof(createdAt), "Date cannot be empty.");
            if (tankerId <= 0) throw new ArgumentOutOfRangeException(nameof(tankerId), "Tanker ID must be greater than zero.");
            if (tankIds == null || !tankIds.Any()) throw new ArgumentException("Tank IDs cannot be null or empty.", nameof(tankIds));

            if (tankIds.Any(tankId => !_tanks.ContainsKey(tankId))) throw new GameEngineException($"Some tanks with ids: {string.Join(", ", tankIds)} were not found in source kind {Kind}");
            var currentTanker = FindTanker(tankerId);
           
            if (currentTanker == null)
            {
                throw new GameEngineException($"Tanker with id: {tankerId} was not found in source kind {Kind}");
            }

            if(!(currentTanker is TankerPending tankerPending))
            {
                throw new GameEngineException($"Tanker with id: {tankerId} is not pending.");
            }

            List<TankReady> tankReadys = new List<TankReady>();

            foreach (var tankId in tankIds)
            {
                if (_tanks.TryGetValue(tankId, out Tank foundTank))
                {
                    if (foundTank is TankReady tankReady)
                    {
                        tankReadys.Add(tankReady);
                    }
                    else
                    {
                        throw new GameEngineException($"Tank with id: {tankId} is not ready.");
                    }
                }
            }

            tankerPending.Add(tankReadys);
            AddLRUTankerContainer(tankerPending);

        }

        internal void AddOrUpdateTanker(Tanker result)
        {
            if (result == null) throw new ArgumentNullException(nameof(result));

            if (result is TankerArchived tankerArchived)
            {
                if (_tankers.ContainsKey(result.Id))
                {
                    _tankers.Remove(result.Id);
                    obsoleteTankers.Add(result.Id, tankerArchived);
                }
                return;
            }
            else if (result is TankerDispatched tankerDispatched)
            {
                if (_tankers.ContainsKey(result.Id))
                {
                    _tankers.Remove(result.Id);
                    obsoleteTankers.Add(result.Id, tankerDispatched);
                }
                return;
            }

            if (_tankers.TryGetValue(result.Id, out Tanker foundTank))
            {
                _tankers[result.Id] = result;
            }
            else { 
                
                _tankers.Add(result.Id, result);
                tankerConsecutive = result.Id;
            }
        }

        internal bool ExistTanker(int tankerId)
        {
            if (tankerId <= 0) throw new ArgumentOutOfRangeException(nameof(tankerId), "Tanker ID must be greater than zero.");

            bool result = _tankers.ContainsKey(tankerId);

            if (!result) result = obsoleteTankers.ContainsKey(tankerId);

            return result;
        }

        internal Tanker FindTanker(int tankerId)
        {
            if (tankerId <= 0) throw new ArgumentNullException(nameof(tankerId));

            if (_tankers.TryGetValue(tankerId, out Tanker foundTanker))
            {
                return foundTanker;
            }
            if (obsoleteTankers.TryGetValue(tankerId, out Tanker foundObsoleteTanker))
            {
                return foundObsoleteTanker;
            }
            throw new GameEngineException($"Tanker with id: {tankerId} was not found in source kind {Kind}");
        }

        internal int GetFirstTankerWith(int tankId)
        {
            if (tankId <= 0) throw new ArgumentOutOfRangeException(nameof(tankId), "Tank ID must be greater than zero.");
            foreach (var tanker in _tankers.Values)
            {
                if (tanker.Tanks.Any(t => t.Id == tankId))
                {
                    return tanker.Id;
                }
            }
            return -1;
        }


        internal List<Tank> BuildTankWithDepositsBetween(DateTime from, DateTime to, FilterTankStatus status, string name = null, string color = null)
        {
            if (from == DateTime.MinValue && to == DateTime.MinValue) throw new ArgumentNullException("Both 'from' and 'to' dates cannot be MinValue.");

            var filteredTanks = FilterTankByStatus(status);
            if(!string.IsNullOrWhiteSpace(name))
            {
                filteredTanks = filteredTanks.Where(t => t.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
            }
            if (!string.IsNullOrWhiteSpace(color))
            {
                filteredTanks = filteredTanks.Where(t => t.ContainerColor.Equals(color, StringComparison.OrdinalIgnoreCase));
            }
            List<Tank> tankList = new List<Tank>();
            foreach (var tank in filteredTanks)
            {

                var hasDeposits = false;
                if (from != DateTime.MinValue && to != DateTime.MinValue)
                {
                    if (from > to) throw new ArgumentException("The 'from' date cannot be greater than the 'to' date.", nameof(from));

                    hasDeposits = tank.BuildDepositsBetween(from, to);
                }
                else if (from != DateTime.MinValue)
                {
                    hasDeposits = tank.BuildDepositsFrom(from);
                }
                else if (to != DateTime.MinValue)
                {
                    hasDeposits = tank.BuildDepositsUpTo(to);
                }

                if (hasDeposits)
                {
                    tankList.Add(tank);
                }
            }
            return tankList;
        }

        internal List<Tank> BuildTankWithDepositsFrom(DateTime from, FilterTankStatus status, string name = null, string color = null)
        {
            if (from == DateTime.MinValue) throw new ArgumentNullException(nameof(from));
            List<Tanker> tankerList = new List<Tanker>();

            return BuildTankWithDepositsBetween(from, DateTime.MinValue, status, name, color);

        }

        internal List<Tank> BuildTankWithDepositsUpTo(DateTime to, FilterTankStatus status, string name = null, string color = null)
        {
            if (to == DateTime.MinValue) throw new ArgumentNullException(nameof(to));
            List<Tanker> tankerList = new List<Tanker>();

            return BuildTankWithDepositsBetween(DateTime.MinValue, to, status, name, color);
        }

        internal List<Tanker> BuildTankerWithDepositsBetween(DateTime from, DateTime to,TankerStatus tankerStatus, string name = null, string color = null)
        {
            if (from == DateTime.MinValue && to == DateTime.MinValue) throw new ArgumentNullException("Both 'from' and 'to' dates cannot be MinValue.");
           
            var filteredTankers = FilterTankersByStatus(tankerStatus);
            if (!string.IsNullOrWhiteSpace(name))
            {
                filteredTankers = filteredTankers.Where(t => t.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
            }
            if (!string.IsNullOrWhiteSpace(color))
            {
                filteredTankers = filteredTankers.Where(t => t.ContainerColor.Equals(color, StringComparison.OrdinalIgnoreCase));
            }

            List<Tanker> tankerList = new List<Tanker>();
            foreach (var tanker in filteredTankers)
            {

                var hasDeposits = false;
                if (from != DateTime.MinValue && to != DateTime.MinValue) 
                {
                   if (from > to) throw new ArgumentException("The 'from' date cannot be greater than the 'to' date.", nameof(from));
                    
                   hasDeposits = tanker.BuildDepositsBetween(from, to);
                }
                else if(from != DateTime.MinValue)
                {
                    hasDeposits = tanker.BuildDepositsFrom(from);
                }
                else if (to != DateTime.MinValue)
                {
                    hasDeposits = tanker.BuildDepositsUpTo(to);
                }

                if (hasDeposits)
                {
                    tankerList.Add(tanker);
                }
            }
            return tankerList;
        }

        internal List<Tanker> BuildTankerWithDepositsFrom(DateTime from, TankerStatus status, string name = null, string color = null)
        {
            if (from == DateTime.MinValue) throw new ArgumentNullException(nameof(from));
            return BuildTankerWithDepositsBetween(from, DateTime.MinValue, status,name,color);

        }

        internal List<Tanker> BuildTankerWithDepositsUpTo(DateTime to, TankerStatus status, string name = null, string color = null)
        {
            if (to == DateTime.MinValue) throw new ArgumentNullException(nameof(to));
            return BuildTankerWithDepositsBetween(DateTime.MinValue, to, status,name ,color);
        }

        private DateTime CalculateMinDateTank()
        {

            if (!_tanks.Any())
                return DateTime.MaxValue;

            DateTime minDate = DateTime.MaxValue;
            foreach (var tank in _tanks.Values)
            {

                if (tank.MinDate < minDate)
                {
                    minDate = tank.MinDate;
                }
            }
            return minDate;
        }
        private DateTime CalculateMaxDateTank()
        {
            if (!_tanks.Any())
                return DateTime.MinValue;

            DateTime maxDate = DateTime.MinValue;
            foreach (var tank in _tanks.Values)
            {
                if (tank.MaxDate > maxDate)
                {
                    maxDate = tank.MaxDate;
                }
            }
            return maxDate;
        }

        private DateTime CalculateMinDateTankers()
        {

            if (!_tankers.Any())
                return DateTime.MaxValue;

            DateTime minDate = DateTime.MaxValue;
            foreach (var tanker in _tankers.Values)
            {

                if (tanker.MinDate < minDate)
                {
                    minDate = tanker.MinDate;
                }
            }
            return minDate;
        }
        private DateTime CalculateMaxDateTankers()
        {
            if (!_tankers.Any())
                return DateTime.MinValue;

            DateTime maxDate = DateTime.MinValue;
            foreach (var tanker in _tankers.Values)
            {
                if (tanker.MaxDate > maxDate)
                {
                    maxDate = tanker.MaxDate;
                }
            }
            return maxDate;
        }

        internal void ClearMaxMinDate()
        {
            maxDateTank = DateTime.MinValue;
            minDateTank = DateTime.MinValue;
            maxDateTanker = DateTime.MinValue;
            minDateTanker = DateTime.MinValue;
        }

       
        internal CompletedDepositSummary BuildRecentDeposits(DateTime now)
        {
            var result = transactionVelocityWindow.GetRecent(now);
            var deposits = new List<Deposit>();
            foreach (var transaction in result)
            {
                if (transaction is Deposit deposit)
                {
                    deposits.Add(deposit);
                }
            }
            return Jar.BuildRecentDeposits(deposits,now);
        }

        internal bool IsValidScheduledDate(DateTime now, DateTime startDate)
        {
            if (startDate == DateTime.MinValue) throw new ArgumentNullException(nameof(startDate));
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));
            return startDate <= now;
        }
        
    }

}
