using GamesEngine;
using GamesEngine.Business.Liquidity.ExternalServices;
using GamesEngine.Business.Liquidity.Persistence;
using GamesEngine.Finance;
using LiquidityAPI;
using Microsoft.AspNetCore.Mvc;
using NBitcoin;
using Newtonsoft.Json;
using System.Globalization;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using static GamesEngine.Business.Liquidity.Persistence.DataModel;
using static LiquidityBIAPI.Controllers.APIController;

namespace LiquidityBIAPI.Controllers
{
    [ApiController]
    [Route("api/liquiditybi")]
    public class APIController : AuthorizeController
    {
        private readonly IOlapQueryService _olapQueryService;
        private readonly ISearchStorage _elasticsearchQueryStore;
        private readonly INodeExplorerClient _nodeExplorerClient;

        public APIController(IOlapQueryService olapQueryService = null,
                             ISearchStorage elasticsearchQueryStore = null,
                             INodeExplorerClient nodeExplorerClient = null)
        {
            _olapQueryService = olapQueryService;
            _elasticsearchQueryStore = elasticsearchQueryStore;
            _nodeExplorerClient = nodeExplorerClient;
        }

        [HttpGet("{currencyCode}/transactions/search")]
        public async Task<ActionResult<IEnumerable<TransactionSearchResult>>> SearchTransactions(string currencyCode, [FromQuery] SearchRequest request)
        {
            if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest("currencyCode is required");
            if (_elasticsearchQueryStore == null) return NotFound("Search service is not available.");

            try
            {
                DateTime? fromDate = null;
                if (!string.IsNullOrEmpty(request.From))
                {
                    if (DateTime.TryParse(request.From, CultureInfo.InvariantCulture, DateTimeStyles.None, out var parsedFrom))
                    {
                        fromDate = parsedFrom;
                    }
                }

                DateTime? toDate = null;
                if (!string.IsNullOrEmpty(request.To))
                {
                    if (DateTime.TryParse(request.To, CultureInfo.InvariantCulture, DateTimeStyles.None, out var parsedTo))
                    {
                        toDate = parsedTo.Date.AddDays(1).AddTicks(-1);
                    }
                }

                var filter = new SearchFilter
                {
                    Account = request.Account,
                    FromDate = fromDate,
                    ToDate = toDate,
                    TransactionId = request.TransactionId,
                    State = request.State,
                    InitialIndex = request.InitialIndex,
                    AmountOfRows = request.AmountOfRows,
                    ExternalId = request.ExternalId,
                    Address = request.Address
                };

                var searchContainer = await _elasticsearchQueryStore.SearchTransactionsAsync(currencyCode, filter);

                var response = new TransactionSearchResponse
                {
                    Transactions = searchContainer.Transactions,
                    TotalDepositsAmount = searchContainer.TotalDeposits,
                    TotalWithdrawalsAmount = searchContainer.TotalWithdrawals,
                    TransactionsCount = searchContainer.TotalTransactions
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest("An error occurred while performing the search.");
            }
        }

        [HttpGet("{currencyCode}/invoices/payments")]
        public async Task<ActionResult<IEnumerable<InvoicePaymentResponse>>> GetInvoicesByExternalAddress(string currencyCode, [FromQuery] string externalAtAddress, [FromQuery] string transactionDate)
        {
            if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest("currencyCode is required");
            if (_elasticsearchQueryStore == null) return NotFound("Search service is not available.");
            if (_nodeExplorerClient == null) return NotFound("Bitcoin node service is not available.");
            if (string.IsNullOrWhiteSpace(externalAtAddress)) return BadRequest("The 'externalAtAddress' query parameter is required.");
            if (string.IsNullOrWhiteSpace(transactionDate)) return BadRequest("The 'transactionDate' query parameter is required.");

            try
            {
                if (!DateTime.TryParse(transactionDate, CultureInfo.InvariantCulture, DateTimeStyles.None, out var filterDate)) return BadRequest("Invalid 'transactionDate' format. Please provide a valid date-time string.");
                
                var invoicePayments = await _elasticsearchQueryStore.InvoicePaymentsAsync(currencyCode, externalAtAddress, filterDate);
                if (invoicePayments == null || !invoicePayments.Any())
                {
                    return Ok(Enumerable.Empty<InvoicePaymentResponse>());
                }

                var finalResponse = new List<InvoicePaymentResponse>();
                var paymentsByAddress = invoicePayments.GroupBy(p => p.DestinationAddress);

                foreach (var addressGroup in paymentsByAddress)
                {
                    var address = addressGroup.Key;
                    decimal liveBalance = await _nodeExplorerClient.AddressBalanceAsync(address, currencyCode);
                    decimal runningBalance = liveBalance;

                    foreach (var payment in addressGroup)
                    {
                        finalResponse.Add(new InvoicePaymentResponse
                        {
                            DestinationAddress = payment.DestinationAddress,
                            ExternalAtAddress = payment.ExternalAtAddress,
                            PaidAmount = payment.PaidAmount,
                            PaidAt = payment.PaidAt.ToString("MM/dd/yyyy HH:mm:ss"),
                            CurrentBalance = runningBalance,
                            PreviousBalance = runningBalance - payment.PaidAmount
                        });

                        runningBalance -= payment.PaidAmount;
                    }
                }

                return Ok(finalResponse.OrderByDescending(p => DateTime.Parse(p.PaidAt, CultureInfo.InvariantCulture)));
            }
            catch (Exception ex)
            {
                return BadRequest("An error occurred while retrieving invoice payments.");
            }
        }

        [HttpGet("{currencyCode}/jar/deposits")]
        public async Task<ActionResult<DepositsInJarResponse>> DepositsInNewestJar(string currencyCode, DateTime startDate, DateTime endDate, string accountNumber = null)
        {
            if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest("currencyCode is required");
            try
            {
                JarWithDeposits jarData = await _olapQueryService.DepositsInNewestJarAsync(currencyCode, startDate, endDate, accountNumber);
                IEnumerable<DepositSummaryResponse> depositSummaries = jarData.Deposits.Select(d => new DepositSummaryResponse
                {
                    Date = d.DateAsText,
                    DomainId = d.DomainId,
                    AccountNumber = d.AccountNumber,
                    Amount = d.Amount,
                    DocumentNumber = d.DocumentNumber,
                    Address = d.Address
                });

                var response = new DepositsInJarResponse
                {
                    TotalAmount = jarData.TotalAmount,
                    DepositCount = jarData.DepositCount,
                    Deposits = depositSummaries
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest("An error occurred while retrieving transactions.");
            }
        }

        [HttpGet("{currencyCode}/legacyJars/{jarId}/deposits")]
        public async Task<ActionResult<DepositsInJarResponse>> DepositsInLegacyJar(string currencyCode, long jarId, DateTime startDate, DateTime endDate, string accountNumber = null)
        {
            if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest("currencyCode is required");
            if (jarId <= 0) return BadRequest($"{nameof(jarId)} {jarId} is not valid.");

            try
            {
                JarWithDeposits jarData = await _olapQueryService.DepositsInLegacyJarAsync(currencyCode, jarId, startDate, endDate, accountNumber);
                IEnumerable<DepositSummaryResponse> depositSummaries = jarData.Deposits.Select(d => new DepositSummaryResponse
                {
                    Date = d.DateAsText,
                    DomainId = d.DomainId,
                    AccountNumber = d.AccountNumber,
                    Amount = d.Amount,
                    DocumentNumber = d.DocumentNumber,
                    Address = d.Address
                });

                var response = new DepositsInJarResponse
                {
                    TotalAmount = jarData.TotalAmount,
                    DepositCount = jarData.DepositCount,
                    Deposits = depositSummaries
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest($"An error occurred while retrieving transactions for Jar ID {jarId}.");
            }
        }

        [HttpGet("{currencyCode}/jar/origin")]
        public async Task<ActionResult<JarComparisonResponse>> NewestJarWithOrigin(string currencyCode)
        {
            if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest("currencyCode is required");
            try
            {
                var jarWithOriginData = await _olapQueryService.NewestJarWithOriginAsync(currencyCode);
                if (jarWithOriginData == null) return NotFound("No jars found for comparison.");

                var currentJarResponse = new JarWithDepositsResponse
                {
                    JarId = jarWithOriginData.CurrentJarId,
                    TotalAmount = jarWithOriginData.CurrentJarData.TotalAmount,
                    DepositCount = jarWithOriginData.CurrentJarData.DepositCount,
                    Deposits = jarWithOriginData.CurrentJarData.Deposits.Select(d => new DepositSummaryResponse
                    {
                        Date = d.DateAsText,
                        DomainId = d.DomainId,
                        AccountNumber = d.AccountNumber,
                        Amount = d.Amount,
                        DocumentNumber = d.DocumentNumber,
                        Address = d.Address
                    })
                };

                JarWithDepositsResponse originJarResponse = null;
                if (jarWithOriginData.OriginJarData != null && jarWithOriginData.OriginJarId.HasValue)
                {
                    originJarResponse = new JarWithDepositsResponse
                    {
                        JarId = jarWithOriginData.OriginJarId.Value,
                        TotalAmount = jarWithOriginData.OriginJarData.TotalAmount,
                        DepositCount = jarWithOriginData.OriginJarData.DepositCount,
                        Deposits = jarWithOriginData.OriginJarData.Deposits.Select(d => new DepositSummaryResponse
                        {
                            Date = d.DateAsText,
                            DomainId = d.DomainId,
                            AccountNumber = d.AccountNumber,
                            Amount = d.Amount,
                            DocumentNumber = d.DocumentNumber,
                            Address = d.Address
                        })
                    };
                }

                var response = new JarComparisonResponse
                {
                    CurrentJar = currentJarResponse,
                    OriginJar = originJarResponse
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest("An error occurred while comparing the newest jar.");
            }
        }

        [HttpGet("{currencyCode}/jar/next")]
        public async Task<ActionResult<JarNextContainerResponse>> NewestJarWithNext(string currencyCode)
        {
            if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest("currencyCode is required");
            try
            {
                var nextContainerData = await _olapQueryService.NextContainerForNewestJarAsync(currencyCode);
                if (nextContainerData == null) return NotFound("No jars found or no next container exists for the newest jar.");

                return Ok(MapToJarNextContainerResponse(nextContainerData));
            }
            catch (Exception ex)
            {
                return BadRequest("An error occurred while retrieving the next container for the newest jar.");
            }
        }

        [HttpGet("{currencyCode}/legacyJars/{jarId}/origin")]
        public async Task<ActionResult<JarComparisonResponse>> LegacyJarWithOrigin(string currencyCode, long jarId)
        {
            if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest("currencyCode is required");
            if (jarId <= 0) return BadRequest($"{nameof(jarId)} {jarId} is not valid.");

            try
            {
                var jarWithOriginData = await _olapQueryService.LegacyJarWithOriginAsync(currencyCode, jarId);
                if (jarWithOriginData == null) return NotFound($"Jar with ID {jarId} not found.");

                var currentJarResponse = new JarWithDepositsResponse
                {
                    JarId = jarId,
                    TotalAmount = jarWithOriginData.CurrentJarData.TotalAmount,
                    DepositCount = jarWithOriginData.CurrentJarData.DepositCount,
                    Deposits = jarWithOriginData.CurrentJarData.Deposits.Select(d => new DepositSummaryResponse
                    {
                        Date = d.DateAsText,
                        DomainId = d.DomainId,
                        AccountNumber = d.AccountNumber,
                        Amount = d.Amount,
                        DocumentNumber = d.DocumentNumber,
                        Address = d.Address
                    })
                };

                JarWithDepositsResponse originJarResponse = null;
                if (jarWithOriginData.OriginJarData != null && jarWithOriginData.OriginJarId.HasValue)
                {
                    originJarResponse = new JarWithDepositsResponse
                    {
                        JarId = jarWithOriginData.OriginJarId.Value,
                        TotalAmount = jarWithOriginData.OriginJarData.TotalAmount,
                        DepositCount = jarWithOriginData.OriginJarData.DepositCount,
                        Deposits = jarWithOriginData.OriginJarData.Deposits.Select(d => new DepositSummaryResponse
                        {
                            Date = d.DateAsText,
                            DomainId = d.DomainId,
                            AccountNumber = d.AccountNumber,
                            Amount = d.Amount,
                            DocumentNumber = d.DocumentNumber,
                            Address = d.Address
                        })
                    };
                }

                var response = new JarComparisonResponse
                {
                    CurrentJar = currentJarResponse,
                    OriginJar = originJarResponse
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest($"An error occurred while comparing jars for Jar ID {jarId}.");
            }
        }

        [HttpGet("{currencyCode}/legacyJars/{jarId}/next")]
        public async Task<ActionResult<JarNextContainerResponse>> LegacyJarWithNext(string currencyCode, long jarId)
        {
            if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest("currencyCode is required");
            if (jarId <= 0) return BadRequest($"{nameof(jarId)} {jarId} is not valid.");

            try
            {
                var nextContainerData = await _olapQueryService.NextContainerForJarAsync(currencyCode, jarId);
                if (nextContainerData == null) return NotFound($"Jar with ID {jarId} not found.");

                return Ok(MapToJarNextContainerResponse(nextContainerData));
            }
            catch (Exception ex)
            {
                return BadRequest($"An error occurred while retrieving next container for Jar ID {jarId}.");
            }
        }

        [HttpGet("{currencyCode}/tanks/{tankId}/deposits")]
        public async Task<ActionResult<TankDepositsResponse>> DepositsByTank(string currencyCode, long tankId)
        {
            if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest("currencyCode is required");
            if (tankId <= 0) return BadRequest($"{nameof(tankId)} {tankId} is not valid.");

            try
            {
                TankWithDeposits tankData = await _olapQueryService.TankAndAllItsDepositsAsync(currencyCode, tankId);
                if (tankData == null || tankData.TankInfo == null) return NotFound($"{nameof(tankId)} {tankId} is not found.");

                IEnumerable<TankDepositSummaryResponse> depositSummaries = tankData.Deposits.Select(d => new TankDepositSummaryResponse
                {
                    Date = d.DateAsText,
                    Amount = d.Amount,
                    DocumentNumber = d.DocumentNumber,
                    StoreId = d.StoreId,
                    AccountNumber = d.AccountNumber
                });

                var responseDto = new TankDepositsResponse
                {
                    TankId = tankData.TankInfo.Id,
                    TankDescription = tankData.TankInfo.Description,
                    OriginType = tankData.TankInfo.OriginType,
                    OriginId = tankData.TankInfo.OriginId,
                    TotalAmount = tankData.TotalDepositsAmount,
                    DepositCount = tankData.DepositsCount,
                    Deposits = depositSummaries
                };

                return Ok(responseDto);
            }
            catch (Exception ex)
            {
                return BadRequest($"An error occurred while retrieving deposit information for Tank ID {tankId}.");
            }
        }

        [HttpGet("{currencyCode}/tanks/{tankId}/origin")]
        public async Task<ActionResult<TankComparisonResponse>> TankWithOrigin(string currencyCode, long tankId)
        {
            if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest("currencyCode is required");
            if (tankId <= 0) return BadRequest($"{nameof(tankId)} {tankId} is not valid.");

            try
            {
                var tankWithOriginData = await _olapQueryService.TankWithOriginAsync(currencyCode, tankId);
                if (tankWithOriginData == null) return NotFound($"Tank with ID {tankId} not found, or it does not have a single Jar as its origin.");

                var currentTankResponse = new TankDepositsResponse
                {
                    TankId = tankWithOriginData.CurrentTankData.TankInfo.Id,
                    TankDescription = tankWithOriginData.CurrentTankData.TankInfo.Description,
                    OriginType = tankWithOriginData.CurrentTankData.TankInfo.OriginType,
                    OriginId = tankWithOriginData.CurrentTankData.TankInfo.OriginId,
                    TotalAmount = tankWithOriginData.CurrentTankData.TotalDepositsAmount,
                    DepositCount = tankWithOriginData.CurrentTankData.DepositsCount,
                    Deposits = tankWithOriginData.CurrentTankData.Deposits.Select(d => new TankDepositSummaryResponse
                    {
                        Date = d.DateAsText,
                        Amount = d.Amount,
                        DocumentNumber = d.DocumentNumber,
                        StoreId = d.StoreId,
                        AccountNumber = d.AccountNumber
                    })
                };

                var response = new TankComparisonResponse
                {
                    CurrentTank = currentTankResponse
                };

                if (tankWithOriginData.OriginJarData != null)
                {
                    response.OriginJar = new JarWithDepositsResponse
                    {
                        JarId = tankWithOriginData.CurrentTankData.TankInfo.OriginId.Value,
                        TotalAmount = tankWithOriginData.OriginJarData.TotalAmount,
                        DepositCount = tankWithOriginData.OriginJarData.DepositCount,
                        Deposits = tankWithOriginData.OriginJarData.Deposits.Select(d => new DepositSummaryResponse
                        {
                            Date = d.DateAsText,
                            DomainId = d.DomainId,
                            AccountNumber = d.AccountNumber,
                            Amount = d.Amount,
                            DocumentNumber = d.DocumentNumber,
                            Address = d.Address
                        })
                    };
                }
                else if (tankWithOriginData.OriginTankData != null)
                {
                    response.OriginTank = new TankDepositsResponse
                    {
                        TankId = tankWithOriginData.OriginTankData.TankInfo.Id,
                        TankDescription = tankWithOriginData.OriginTankData.TankInfo.Description,
                        OriginType = tankWithOriginData.OriginTankData.TankInfo.OriginType,
                        OriginId = tankWithOriginData.OriginTankData.TankInfo.OriginId,
                        TotalAmount = tankWithOriginData.OriginTankData.TotalDepositsAmount,
                        DepositCount = tankWithOriginData.OriginTankData.DepositsCount,
                        Deposits = tankWithOriginData.OriginTankData.Deposits.Select(d => new TankDepositSummaryResponse
                        {
                            Date = d.DateAsText,
                            Amount = d.Amount,
                            DocumentNumber = d.DocumentNumber,
                            StoreId = d.StoreId,
                            AccountNumber = d.AccountNumber
                        })
                    };
                }

                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest($"An error occurred while retrieving origin information for Tank ID {tankId}.");
            }
        }

        [HttpGet("{currencyCode}/tanks/{tankId}/next")]
        public async Task<ActionResult<TankNextContainerResponse>> TankWithNext(string currencyCode, long tankId)
        {
            if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest("currencyCode is required");
            if (tankId <= 0) return BadRequest($"{nameof(tankId)} {tankId} is not valid.");

            try
            {
                var nextContainerData = await _olapQueryService.NextContainerForTankAsync(currencyCode, tankId);
                if (nextContainerData == null) return NotFound($"Tank with ID {tankId} not found.");

                var currentTankResponse = new TankDepositsResponse
                {
                    TankId = nextContainerData.CurrentTankData.TankInfo.Id,
                    TankDescription = nextContainerData.CurrentTankData.TankInfo.Description,
                    OriginType = nextContainerData.CurrentTankData.TankInfo.OriginType,
                    OriginId = nextContainerData.CurrentTankData.TankInfo.OriginId,
                    TotalAmount = nextContainerData.CurrentTankData.TotalDepositsAmount,
                    DepositCount = nextContainerData.CurrentTankData.DepositsCount,
                    Deposits = nextContainerData.CurrentTankData.Deposits.Select(d => new TankDepositSummaryResponse
                    {
                        Date = d.DateAsText,
                        Amount = d.Amount,
                        DocumentNumber = d.DocumentNumber,
                        StoreId = d.StoreId,
                        AccountNumber = d.AccountNumber
                    })
                };

                var response = new TankNextContainerResponse
                {
                    CurrentTank = currentTankResponse
                };

                if (nextContainerData.NextTankData != null)
                {
                    response.NextTank = new TankDepositsResponse
                    {
                        TankId = nextContainerData.NextTankId.Value,
                        TankDescription = nextContainerData.NextTankData.TankInfo.Description,
                        OriginType = nextContainerData.NextTankData.TankInfo.OriginType,
                        OriginId = nextContainerData.NextTankData.TankInfo.OriginId,
                        TotalAmount = nextContainerData.NextTankData.TotalDepositsAmount,
                        DepositCount = nextContainerData.NextTankData.DepositsCount,
                        Deposits = nextContainerData.NextTankData.Deposits.Select(d => new TankDepositSummaryResponse
                        {
                            Date = d.DateAsText,
                            Amount = d.Amount,
                            DocumentNumber = d.DocumentNumber,
                            StoreId = d.StoreId,
                            AccountNumber = d.AccountNumber
                        })
                    };
                }
                else if (nextContainerData.NextTankerData != null)
                {
                    response.NextTanker = new TankerDepositsResponse
                    {
                        TankerId = nextContainerData.NextTankerId.Value,
                        TankerDescription = nextContainerData.NextTankerData.TankerInfo.Description,
                        TotalAmount = nextContainerData.NextTankerData.TotalDepositsAmount,
                        DepositCount = nextContainerData.NextTankerData.DepositsCount,
                        Deposits = nextContainerData.NextTankerData.Deposits.Select(d => new TankerDepositSummaryResponse
                        {
                            Date = d.DateAsText,
                            Amount = d.Amount,
                            DocumentNumber = d.DocumentNumber,
                            StoreId = d.StoreId,
                            AccountNumber = d.AccountNumber
                        })
                    };
                }

                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest($"An error occurred while retrieving next container information for Tank ID {tankId}.");
            }
        }

        [HttpGet("{currencyCode}/tanks/{tankId}/compare/{version}")]
        public async Task<ActionResult<TankVersionComparisonResponse>> CompareTankVersions(string currencyCode, long tankId, int version)
        {
            if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest("currencyCode is required");
            if (tankId <= 0) return BadRequest($"{nameof(tankId)} {tankId} is not valid.");
            if (version <= 0) return BadRequest($"{nameof(version)} {version} is not valid.");
            if (_olapQueryService == null) return NotFound("Storage service is not available.");

            try
            {
                var comparisonData = await _olapQueryService.TankVersionComparisonAsync(currencyCode, tankId, version);
                if (comparisonData?.CurrentVersionTank == null) return NotFound($"Tank with ID {tankId} and version {version} not found.");

                var currentTransactions = comparisonData.CurrentVersionDeposits
                    .Select(d => new TankTransactionSummary
                    {
                        Id = d.Id,
                        CreatedAt = d.Date.ToString("MM/dd/yyyy"),
                        DomainId = d.DomainId,
                        ExternalAtAddress = d.DocumentNumber,
                        Amount = d.Amount,
                        ConfirmedCurrency = "USD",
                        ConfirmedAmount = Math.Round(d.Amount * d.Rate, 2),
                        Destination = d.Address
                    }).ToList();

                var response = new TankVersionComparisonResponse
                {
                    TankId = tankId,
                    Version = version,
                    TankDescription = comparisonData.CurrentVersionTank.Description,
                    Transactions = currentTransactions,
                    DepositCount = currentTransactions.Count,
                    TotalAmount = currentTransactions.Sum(t => t.Amount),
                    HasNext = comparisonData.NextVersion.HasValue,
                    NextTankVersion = comparisonData.NextVersion,
                    IsTankObsolete = true
                };

                if (comparisonData.PreviousVersionTank != null)
                {
                    var previousTransactions = comparisonData.PreviousVersionDeposits
                        .Select(d => new TankTransactionSummary
                        {
                            Id = d.Id,
                            CreatedAt = d.Date.ToString("MM/dd/yyyy"),
                            DomainId = d.DomainId,
                            ExternalAtAddress = d.DocumentNumber,
                            Amount = d.Amount,
                            ConfirmedCurrency = "USD",
                            ConfirmedAmount = Math.Round(d.Amount * d.Rate, 2),
                            Destination = d.Address
                        }).ToList();

                    response.HasPrevious = true;
                    response.PreviousVersion = comparisonData.PreviousVersionTank.Version;
                    response.PreviousTankId = tankId;
                    response.PreviousTankDescription = comparisonData.PreviousVersionTank.Description;
                    response.PreviousTankTransactions = previousTransactions;
                    response.PreviousTankDepositCount = previousTransactions.Count;
                    response.PreviousTankTotalAmount = previousTransactions.Sum(t => t.Amount);
                }
                else
                {
                    response.HasPrevious = false;
                }

                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest($"An error occurred while comparing tank versions for Tank ID {tankId}.");
            }
        }

        [HttpGet("{currencyCode}/tankers/{tankerId}/compare/{version}")]
        public async Task<ActionResult<TankerVersionComparisonResponse>> CompareTankerVersions(string currencyCode, long tankerId, int version)
        {
            if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest("currencyCode is required");
            if (tankerId <= 0) return BadRequest($"{nameof(tankerId)} {tankerId} is not valid.");
            if (version <= 0) return BadRequest($"{nameof(version)} {version} is not valid.");
            if (_olapQueryService == null) return NotFound("Storage service is not available.");

            try
            {
                var comparisonData = await _olapQueryService.TankerVersionComparisonAsync(currencyCode, tankerId, version);
                if (comparisonData?.CurrentVersionTanker == null) return NotFound($"Tanker with ID {tankerId} and version {version} not found.");

                var currentTransactions = comparisonData.CurrentVersionDeposits
                    .Select(d => new TankTransactionSummary
                    {
                        Id = d.Id,
                        CreatedAt = d.Date.ToString("MM/dd/yyyy"),
                        DomainId = d.DomainId,
                        ExternalAtAddress = d.DocumentNumber,
                        Amount = d.Amount,
                        ConfirmedCurrency = "USD",
                        ConfirmedAmount = Math.Round(d.Amount * d.Rate, 2),
                        Destination = d.Address
                    }).ToList();

                var response = new TankerVersionComparisonResponse
                {
                    TankerId = tankerId,
                    Version = version,
                    TankerDescription = comparisonData.CurrentVersionTanker.Description,
                    Transactions = currentTransactions,
                    DepositCount = currentTransactions.Count,
                    TotalAmount = currentTransactions.Sum(t => t.Amount),
                    HasNext = comparisonData.NextVersion.HasValue,
                    NextTankerVersion = comparisonData.NextVersion,
                    IsTankerObsolete = true
                };

                if (comparisonData.PreviousVersionTanker != null)
                {
                    var previousTransactions = comparisonData.PreviousVersionDeposits
                        .Select(d => new TankTransactionSummary
                        {
                            Id = d.Id,
                            CreatedAt = d.Date.ToString("MM/dd/yyyy"),
                            DomainId = d.DomainId,
                            ExternalAtAddress = d.DocumentNumber,
                            Amount = d.Amount,
                            ConfirmedCurrency = "USD",
                            ConfirmedAmount = Math.Round(d.Amount * d.Rate, 2),
                            Destination = d.Address
                        }).ToList();

                    response.HasPrevious = true;
                    response.PreviousTankerVersion = comparisonData.PreviousVersionTanker.Version;
                    response.PreviousTankerId = tankerId;
                    response.PreviousTankerDescription = comparisonData.PreviousVersionTanker.Description;
                    response.PreviousTankerTransactions = previousTransactions;
                    response.PreviousTankerDepositCount = previousTransactions.Count;
                    response.PreviousTankerTotalAmount = previousTransactions.Sum(t => t.Amount);
                }
                else
                {
                    response.HasPrevious = false;
                }

                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest($"An error occurred while comparing tanker versions for Tanker ID {tankerId}.");
            }
        }

        [HttpGet("{currencyCode}/tankers/{tankerId}/deposits")]
        public async Task<ActionResult<TankerDepositsResponse>> DepositsByTanker(string currencyCode, long tankerId)
        {
            if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest("currencyCode is required");
            if (tankerId <= 0) return BadRequest($"{nameof(tankerId)} {tankerId} is not valid.");

            try
            {
                TankerWithDeposits tankerData = await _olapQueryService.TankerAndAllItsDepositsAsync(currencyCode, tankerId);
                if (tankerData == null || tankerData.TankerInfo == null) return NotFound($"{nameof(tankerId)} {tankerId} is not found.");

                IEnumerable<TankerDepositSummaryResponse> depositSummaries = tankerData.Deposits.Select(d => new TankerDepositSummaryResponse
                {
                    Date = d.DateAsText,
                    Amount = d.Amount,
                    DocumentNumber = d.DocumentNumber,
                    StoreId = d.StoreId,
                    AccountNumber = d.AccountNumber
                });

                var responseDto = new TankerDepositsResponse
                {
                    TankerId = tankerData.TankerInfo.Id,
                    TankerDescription = tankerData.TankerInfo.Description,
                    TotalAmount = tankerData.TotalDepositsAmount,
                    DepositCount = tankerData.DepositsCount,
                    Deposits = depositSummaries
                };

                return Ok(responseDto);
            }
            catch (Exception ex)
            {
                return BadRequest($"An error occurred while retrieving deposit information for Tanker ID {tankerId}.");
            }
        }

        [HttpGet("{currencyCode}/tankers/{tankerId}/depositsAndAddresses")]
        public async Task<IActionResult> DepositsByTankerWithAddress(string currencyCode, long tankerId)
        {
            if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest("currencyCode is required");
            if (tankerId <= 0) return BadRequest($"{nameof(tankerId)} {tankerId} is not valid.");

            try
            {
                TankerWithDeposits tankerData = await _olapQueryService.TankerAndAllItsDepositsAsync(currencyCode, tankerId);
                if (tankerData == null || tankerData.TankerInfo == null) return NotFound($"{nameof(tankerId)} {tankerId} is not found.");

                IEnumerable<DepositAmountAndAddress> depositSummaries = tankerData.Deposits.Select(d => new DepositAmountAndAddress
                {
                    Amount = ConvertAmount(d.Amount, currencyCode),
                    Address = d.Address
                });

                var responseDto = new TankerSimpleDepositsResponse
                {
                    Id = tankerData.TankerInfo.Id,
                    Name = tankerData.TankerInfo.Name,
                    Description = tankerData.TankerInfo.Description,
                    Deposits = depositSummaries
                };

                try
                {
                    return GenerateCsvFile(tankerId, responseDto);
                }
                catch (JsonException ex)
                {
                    return BadRequest("Internal error processing csv data.");
                }
            }
            catch (Exception ex)
            {
                return BadRequest($"An error occurred while retrieving deposit information for Tanker ID {tankerId}.");
            }
        }

        [HttpGet("{currencyCode}/tankers/{tankerId}/depositsAndAddresses/psbt")]
        public async Task<IActionResult> DepositsByTankerWithAddressPsbt(string currencyCode, long tankerId)
        {
            if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest("currencyCode is required");
            if (tankerId <= 0) return BadRequest($"{nameof(tankerId)} {tankerId} is not valid.");

            try
            {
                TankerWithDeposits tankerData = await _olapQueryService.TankerAndAllItsDepositsAsync(currencyCode, tankerId);
                if (tankerData == null || tankerData.TankerInfo == null) return NotFound($"{nameof(tankerId)} {tankerId} is not found.");

                IEnumerable<DepositAmountAndAddress> depositSummaries = tankerData.Deposits.Select(d => new DepositAmountAndAddress
                {
                    Amount = ConvertAmount(d.Amount, currencyCode),
                    Address = d.Address
                });

                var responseDto = new TankerSimpleDepositsResponse
                {
                    Id = tankerData.TankerInfo.Id,
                    Name = tankerData.TankerInfo.Name,
                    Description = tankerData.TankerInfo.Description,
                    Deposits = depositSummaries
                };

                try
                {
                    return await GeneratePsbtFileAsync(currencyCode, tankerId, responseDto);
                }
                catch (JsonException ex)
                {
                    return BadRequest("Internal error processing tanker data.");
                }
            }
            catch (Exception ex)
            {
                return BadRequest($"An error occurred while retrieving deposit information for Tanker ID {tankerId}.");
            }
        }

        private IActionResult GenerateCsvFile(long tankerId, TankerSimpleDepositsResponse responseData)
        {
            if (responseData == null) return BadRequest("Failed to parse tanks response.");

            var csvBuilder = new StringBuilder();
            var tankerDescription = string.IsNullOrEmpty(responseData.Description) ? responseData.Name : SanitizeCsvField(responseData.Description);
            if (string.IsNullOrEmpty(tankerDescription)) tankerDescription = "No Description";
            if (responseData.Deposits == null || !responseData.Deposits.Any()) return BadRequest("Failed to parse deposits response.");
            foreach (var deposit in responseData.Deposits)
            {
                if (string.IsNullOrWhiteSpace(deposit.Address)) return BadRequest("Address is missing in one of the deposits.");
                if (deposit.Amount <= 0) return BadRequest("Amount must be greater than zero in all deposits.");
                csvBuilder.AppendLine($"{deposit.Address},{deposit.Amount},{tankerDescription}");
            }
            
            var csvBytes = Encoding.UTF8.GetBytes(csvBuilder.ToString());
            var fileName = $"tanker-{tankerId}-deposits.csv";

            return File(csvBytes, "text/csv", fileName);
        }

        private string SanitizeCsvField(string fieldData)
        {
            if (string.IsNullOrEmpty(fieldData)) return string.Empty;

            if (fieldData.IndexOf(',') != -1 || fieldData.IndexOf('"') != -1)
            {
                var sanitizedField = fieldData.Replace("\"", "\"\"");
                return $"\"{sanitizedField}\"";
            }

            return fieldData;
        }

        private async Task<IActionResult> GeneratePsbtFileAsync(string currencyCode, long tankerId, TankerSimpleDepositsResponse responseData)
        {
            if (currencyCode != "BTC") return BadRequest("currencyCode BTC is required to generate PSBT file");
            if (responseData == null || responseData.Deposits == null || !responseData.Deposits.Any()) return BadRequest("Failed to parse deposits response.");

            try
            {
                var recipients = new List<(string address, Money amount)>();

                foreach (var deposit in responseData.Deposits)
                {
                    if (string.IsNullOrWhiteSpace(deposit.Address)) return BadRequest("Address is missing in one of the deposits.");
                    if (deposit.Amount <= 0) return BadRequest("Amount must be greater than zero in all deposits.");

                    var amountInSat = (long)deposit.Amount;
                    var money = Money.Satoshis(amountInSat);
                    recipients.Add((deposit.Address, money));
                }

                if (recipients.Count == 0) return BadRequest("No valid deposits found to create PSBT.");

                var httpClient = new HttpClient();
                var nodeExplorerClient = new NodeExplorerClient(httpClient);
                var psbt = await nodeExplorerClient.GenerateMultiOutputPSBTAsync(recipients);

                var psbtBytes = psbt.ToBytes();
                var fileName = $"tanker-{tankerId}-deposits.psbt";

                return File(psbtBytes, "application/octet-stream", fileName);
            }
            catch (Exception ex)
            {
                return BadRequest($"Failed to generate PSBT: {ex.Message}");
            }
        }

        [HttpGet("{currencyCode}/tankers/{tankerId}/summary")]
        public async Task<ActionResult<TankerMonthlyBreakdownResponse>> TankerMonthlyBreakdown(string currencyCode, long tankerId)
        {
            if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest("currencyCode is required");
            if (tankerId <= 0) return BadRequest($"{nameof(tankerId)} {tankerId} is not valid.");
            if (_olapQueryService == null) return NotFound("Storage service is not available.");

            try
            {
                var tankerData = await _olapQueryService.TankerMonthlyBreakdownAsync(currencyCode, tankerId);
                if (tankerData == null || tankerData.TankerInfo == null) return NotFound($"Tanker with ID {tankerId} not found.");

                var monthDataList = tankerData.Deposits
                    .GroupBy(d => new DateTime(d.DepositDate.Year, d.DepositDate.Month, 1,0,0,0))
                    .OrderBy(g => g.Key)
                    .Select(monthGroup => {
                        var monthKey = monthGroup.Key;

                        var tanksInMonth = monthGroup
                            .Where(d => d.SourceTankId.HasValue && d.SourceTankId.Value > 0)
                            .GroupBy(d => d.SourceTankId.Value)
                            .Select(tankDepositsGroup =>
                            {
                                var firstDeposit = tankDepositsGroup.First();
                                return new TankInMonth
                                {
                                    Id = tankDepositsGroup.Key,
                                    Name = firstDeposit.TankDescription,
                                    Status = "TankArchived",
                                    Amount = tankDepositsGroup.Sum(d => d.DepositAmount),
                                    CreatedAt = firstDeposit.TankCreatedAt?.ToString("MM/dd/yyyy HH:mm:ss")
                                };
                            }).ToList();

                        var rootDepositsInMonth = monthGroup
                            .Where(d => !d.SourceTankId.HasValue || d.SourceTankId.Value == 0)
                            .Select(d => new RootDepositInMonth
                            {
                                Id = d.DepositId,
                                InvoiceId = d.DepositDocumentNumber,
                                Amount = d.DepositAmount,
                                CreatedAt = d.DepositDate.ToString("MM/dd/yyyy HH:mm:ss")
                            }).ToList();

                        return new MonthData
                        {
                            Month = monthKey.ToString("MM/yyyy"),
                            MonthTotal = monthGroup.Sum(d => d.DepositAmount),
                            Tanks = tanksInMonth.Any() ? tanksInMonth : null,
                            RootDeposits = rootDepositsInMonth.Any() ? rootDepositsInMonth : null
                        };
                    }).ToList();

                var response = new TankerMonthlyBreakdownResponse
                {
                    TankerId = tankerData.TankerInfo.Id,
                    TankerName = tankerData.TankerInfo.Description,
                    Description = tankerData.TankerInfo.Description,
                    TotalAmount = tankerData.TotalAmount,
                    MonthData = monthDataList
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest($"An error occurred while retrieving breakdown for Tanker ID {tankerId}.");
            }
        }

        private const decimal SatoshisPerBtc = 100_000_000m;

        private decimal ConvertAmount(decimal amount, string currencyCode)
        {
            if (currencyCode.Equals("BTC", StringComparison.OrdinalIgnoreCase))
            {
                return amount * SatoshisPerBtc;
            }
            return amount;
        }

        [HttpGet("{currencyCode}/dispensers/{dispenserId}/withdrawals")]
        public async Task<ActionResult<DispenserWithdrawalsResponse>> WithdrawalsByDispenser(string currencyCode, long dispenserId)
        {
            if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest("currencyCode is required");
            if (dispenserId <= 0) return BadRequest($"{nameof(dispenserId)} {dispenserId} is not valid.");

            try
            {
                var dispenserData = await _olapQueryService.DispenserAndAllItsWithdrawalsAsync(currencyCode, dispenserId);
                if (dispenserData == null || dispenserData.DispenserInfo == null || dispenserData.DispenserInfo.Id == 0) return NotFound($"{nameof(dispenserId)} {dispenserId} is not found.");

                var withdrawalSummaries = dispenserData.Withdrawals.Select(w => new DispenserWithdrawalSummaryResponse
                {
                    Date = w.DateAsText,
                    Amount = w.Amount,
                    DocumentNumber = w.DocumentNumber,
                    StoreId = w.StoreId,
                    AccountNumber = w.AccountNumber
                });

                var responseDto = new DispenserWithdrawalsResponse
                {
                    DispenserId = dispenserData.DispenserInfo.Id,
                    DispenserDescription = dispenserData.DispenserInfo.Description,
                    TotalAmount = dispenserData.TotalWithdrawalsAmount,
                    WithdrawalCount = dispenserData.WithdrawalsCount,
                    Withdrawals = withdrawalSummaries
                };

                return Ok(responseDto);
            }
            catch (Exception ex)
            {
                return BadRequest($"An error occurred while retrieving withdrawal information for Dispenser ID {dispenserId}.");
            }
        }

        [HttpGet("{currencyCode}/dispensers/{dispenserId}/compare/{version}")]
        public async Task<ActionResult<DispenserVersionComparisonResponse>> CompareDispenserVersions(string currencyCode, long dispenserId, int version)
        {
            if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest("currencyCode is required");
            if (dispenserId <= 0) return BadRequest($"{nameof(dispenserId)} {dispenserId} is not valid.");
            if (version <= 0) return BadRequest($"{nameof(version)} {version} is not valid.");
            if (_olapQueryService == null) return NotFound("Storage service is not available.");

            try
            {
                var comparisonData = await _olapQueryService.DispenserVersionComparisonAsync(currencyCode, dispenserId, version);
                if (comparisonData?.CurrentVersionDispenser == null) return NotFound($"Dispenser with ID {dispenserId} and version {version} not found.");

                var currentTransactions = comparisonData.CurrentVersionWithdrawals
                    .Select(w => new DispenserTransactionSummary
                    {
                        Id = w.Id,
                        CreatedAt = w.Date.ToString("MM/dd/yyyy"),
                        DomainId = w.DomainId,
                        AtAddress = w.Address,
                        Amount = w.Amount,
                        ReceivedAmount = Math.Round(w.Amount * w.Rate, 2),
                        Destination = w.AccountNumber
                    }).ToList();

                var response = new DispenserVersionComparisonResponse
                {
                    DispenserId = dispenserId,
                    Version = version,
                    Name = comparisonData.CurrentVersionDispenser.Description,
                    DispenserDescription = comparisonData.CurrentVersionDispenser.Description,
                    Transactions = currentTransactions,
                    WithdrawalCount = currentTransactions.Count,
                    TotalAmount = currentTransactions.Sum(t => t.Amount),
                    HasNext = comparisonData.NextVersion.HasValue,
                    NextDispenserVersion = comparisonData.NextVersion,
                    IsDispenserObsolete = true
                };

                if (comparisonData.PreviousVersionDispenser != null)
                {
                    var previousTransactions = comparisonData.PreviousVersionWithdrawals
                        .Select(w => new DispenserTransactionSummary
                        {
                            Id = w.Id,
                            CreatedAt = w.Date.ToString("MM/dd/yyyy"),
                            DomainId = w.DomainId,
                            AtAddress = w.Address,
                            Amount = w.Amount,
                            ReceivedAmount = Math.Round(w.Amount * w.Rate, 2),
                            Destination = w.AccountNumber
                        }).ToList();

                    response.HasPrevious = true;
                    response.PreviousDispenserVersion = comparisonData.PreviousVersionDispenser.Version;
                    response.PreviousDispenserId = dispenserId;
                    response.PreviousName = comparisonData.PreviousVersionDispenser.Description;
                    response.PreviousDispenserDescription = comparisonData.PreviousVersionDispenser.Description;
                    response.PreviousDispenserTransactions = previousTransactions;
                    response.PreviousDispenserWithdrawalCount = previousTransactions.Count;
                    response.PreviousDispenserTotalAmount = previousTransactions.Sum(t => t.Amount);
                }
                else
                {
                    response.HasPrevious = false;
                }

                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest($"An error occurred while comparing dispenser versions for Dispenser ID {dispenserId}.");
            }
        }

        [HttpGet("{currencyCode}/accounts/{accountNumber}/lastWithdrawalAddresses")]
        public async Task<ActionResult<LastWithdrawalAddressesResponse>> LastWithdrawalAddresses(string currencyCode, string accountNumber)
        {
            if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest("currencyCode is required");
            if (string.IsNullOrWhiteSpace(accountNumber)) return BadRequest("AccountNumber cannot be empty.");
            if (_olapQueryService == null) return NotFound("Storage service is not available.");
            
            const int limit = 3;
            try
            {
                var addresses = await _olapQueryService.LastWithdrawalAddressesAsync(currencyCode, accountNumber, limit);

                var response = new LastWithdrawalAddressesResponse
                {
                    Addresses = addresses
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest($"An error occurred while retrieving last withdrawal addresses for account {accountNumber}.");
            }
        }

        [HttpGet("{currencyCode}/reports/financial")]
        public async Task<ActionResult<DailyDomainTransactionsResponse>> DailyDomainTransactions(string currencyCode, DateTime startDate, DateTime endDate, int? domainId = null)
        {
            if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest("currencyCode is required");
            if (startDate > endDate) return BadRequest("startDate cannot be after endDate.");
            if (_olapQueryService == null) return NotFound("Storage service is not available.");

            try
            {
                IEnumerable<DailyDomainSummaryData> summaryData = await _olapQueryService.DailyDomainSummariesAsync(currencyCode, startDate, endDate, domainId);

                var responseSummaries = summaryData.Select(s => new DailyDomainSummaryResponse
                {
                    Date = s.TransactionDate.ToString("MM/dd/yyyy"),
                    DomainId = s.DomainId,
                    TotalDepositsAmount = s.TotalDepositsAmount,
                    TotalWithdrawalsAmount = s.TotalWithdrawalsAmount,
                    NetTransactionsAmount = s.TotalDepositsAmount - s.TotalWithdrawalsAmount,
                    DepositsCount = s.DepositsCount,
                    WithdrawalsCount = s.WithdrawalsCount,
                    TotalTransactionsCount = s.DepositsCount + s.WithdrawalsCount
                }).ToList();

                return Ok(new DailyDomainTransactionsResponse { Summaries = responseSummaries });
            }
            catch (Exception ex)
            {
                return BadRequest("An error occurred while retrieving daily transaction summaries.");
            }
        }

        [HttpGet("{currencyCode}/reports/dailyDeposits")]
        public async Task<ActionResult<DailyDepositsByDomainResponse>> DailyDepositsByDomain(string currencyCode, [FromQuery] DateTime startDate, [FromQuery] DateTime endDate, [FromQuery] int? domainId = null)
        {
            if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest("currencyCode is required");
            if (startDate > endDate) return BadRequest("startDate cannot be after endDate.");
            if (_olapQueryService == null) return NotFound("Storage service is not available.");

            try
            {
                var exchangeRateResult = ExchangeRate.LastKnown[currencyCode];
                if (!exchangeRateResult.IsSuccess) return BadRequest($"Could not retrieve current exchange rate for {currencyCode}: {exchangeRateResult.ErrorMessage}");
                
                var currentRate = exchangeRateResult.Value;
                var inclusiveEndDate = endDate.Date.AddDays(1).AddTicks(-1);
                var summaryData = await _olapQueryService.DailyDepositSummariesAsync(currencyCode, startDate.Date, inclusiveEndDate, domainId);

                var responseSummaries = summaryData.Select(s =>
                {
                    var totalAmountUSD = Math.Round(s.TotalAmount * currentRate, 2);
                    var fxGainLoss = Math.Round(totalAmountUSD - s.TotalAmountAtOriginalRate, 2);
                    return new DailyDepositSummaryItem
                    {
                        Date = s.TransactionDate.ToString("MM/dd/yyyy"),
                        DomainId = s.DomainId,
                        Transactions = s.TransactionCount,
                        TotalAmount = s.TotalAmount,
                        TotalAmountUSD = totalAmountUSD,
                        FxGainLoss = fxGainLoss
                    };
                }).ToList();

                var grandTotal = responseSummaries.Sum(s => s.TotalAmount);
                var grandTotalUSD = responseSummaries.Sum(s => s.TotalAmountUSD);
                var response = new DailyDepositsByDomainResponse
                {
                    DepositsSummaries = responseSummaries,
                    TotalAmount = grandTotal,
                    TotalAmountUSD = grandTotalUSD
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest("An error occurred while retrieving daily deposit summaries.");
            }
        }

        [HttpGet("{currencyCode}/reports/dailyDepositsDetail")]
        public async Task<ActionResult<DailyDepositsDetailResponse>> DailyDepositsDetailByDomain(string currencyCode, [FromQuery] DateTime date, [FromQuery] int domainId)
        {
            if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest("currencyCode is required");
            if (_olapQueryService == null) return NotFound("Storage service is not available.");

            try
            {
                var startOfDay = date.Date;
                var endOfDay = startOfDay.AddDays(1).AddTicks(-1);
                var deposits = await _olapQueryService.DepositsByDateRangeAsync(currencyCode, startOfDay, endOfDay, domainId);

                var depositDetails = deposits.Select(d => new DailyDepositDetailItem
                {
                    DocumentNumber = d.DocumentNumber,
                    Address = d.Address,
                    Amount = d.Amount
                }).OrderByDescending(det => det.Amount).ToList();

                var response = new DailyDepositsDetailResponse
                {
                    Date = date.ToString("MM/dd/yyyy"),
                    DomainId = domainId,
                    TransactionCount = depositDetails.Count,
                    TotalAmount = deposits.Sum(d => d.Amount),
                    Details = depositDetails
                };
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest("An error occurred while retrieving daily deposit details.");
            }
        }

        [HttpGet("{currencyCode}/reports/dailyWithdrawals")]
        public async Task<ActionResult<DailyWithdrawalsByDomainResponse>> DailyWithdrawalsByDomain(string currencyCode, [FromQuery] DateTime startDate, [FromQuery] DateTime endDate, [FromQuery] int? domainId = null)
        {
            if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest("currencyCode is required");
            if (startDate > endDate) return BadRequest("startDate cannot be after endDate.");
            if (_olapQueryService == null) return NotFound("Storage service is not available.");

            try
            {
                var exchangeRateResult = ExchangeRate.LastKnown[currencyCode];
                if (!exchangeRateResult.IsSuccess) return BadRequest($"Could not retrieve current exchange rate for {currencyCode}: {exchangeRateResult.ErrorMessage}");

                var currentRate = exchangeRateResult.Value;
                var inclusiveEndDate = endDate.Date.AddDays(1).AddTicks(-1);
                var summaryData = await _olapQueryService.DailyWithdrawalSummariesAsync(currencyCode, startDate.Date, inclusiveEndDate, domainId);

                var responseSummaries = summaryData.Select(s =>
                {
                    var totalAmountUSD = Math.Round(s.TotalAmount * currentRate, 2);
                    var fxGainLoss = Math.Round(totalAmountUSD - s.TotalAmountAtOriginalRate, 2);
                    return new DailyWithdrawalSummaryItem
                    {
                        Date = s.TransactionDate.ToString("MM/dd/yyyy"),
                        DomainId = s.DomainId,
                        Transactions = s.TransactionCount,
                        TotalAmount = s.TotalAmount,
                        TotalAmountUSD = totalAmountUSD,
                        FxGainLoss = fxGainLoss
                    };
                }).ToList();

                var grandTotal = responseSummaries.Sum(s => s.TotalAmount);
                var grandTotalUSD = responseSummaries.Sum(s => s.TotalAmountUSD);
                var response = new DailyWithdrawalsByDomainResponse
                {
                    WithdrawalsSummaries = responseSummaries,
                    TotalAmount = grandTotal,
                    TotalAmountUSD = grandTotalUSD
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest("An error occurred while retrieving daily withdrawal summaries.");
            }
        }

        [HttpGet("{currencyCode}/reports/dailyWithdrawalsDetail")]
        public async Task<ActionResult<DailyWithdrawalsDetailResponse>> DailyWithdrawalsDetailByDomain(string currencyCode, [FromQuery] DateTime date, [FromQuery] int domainId)
        {
            if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest("currencyCode is required");
            if (_olapQueryService == null) return NotFound("Storage service is not available.");

            try
            {
                var startOfDay = date.Date;
                var endOfDay = startOfDay.AddDays(1).AddTicks(-1);
                var withdrawals = await _olapQueryService.WithdrawalsByDateRangeAsync(currencyCode, startOfDay, endOfDay, domainId);

                var withdrawalDetails = withdrawals.Select(w => new DailyWithdrawalDetailItem
                {
                    DocumentNumber = w.DocumentNumber,
                    Address = w.Address,
                    Amount = w.Amount
                }).OrderByDescending(det => det.Amount).ToList();

                var response = new DailyWithdrawalsDetailResponse
                {
                    Date = date.ToString("MM/dd/yyyy"),
                    DomainId = domainId,
                    TransactionCount = withdrawalDetails.Count,
                    TotalAmount = withdrawals.Sum(w => w.Amount),
                    Details = withdrawalDetails
                };
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest("An error occurred while retrieving daily withdrawal details.");
            }
        }

        private JarNextContainerResponse MapToJarNextContainerResponse(NextContainerForJar data)
        {
            var currentJarResponse = new JarWithDepositsResponse
            {
                JarId = data.CurrentJarId,
                TotalAmount = data.CurrentJarData.TotalAmount,
                DepositCount = data.CurrentJarData.DepositCount,
                Deposits = data.CurrentJarData.Deposits.Select(d => new DepositSummaryResponse
                {
                    Date = d.DateAsText,
                    DomainId = d.DomainId,
                    AccountNumber = d.AccountNumber,
                    Amount = d.Amount,
                    DocumentNumber = d.DocumentNumber,
                    Address = d.Address
                })
            };

            var response = new JarNextContainerResponse
            {
                CurrentJar = currentJarResponse
            };

            if (data.NextJarData != null)
            {
                response.NextJar = new JarWithDepositsResponse
                {
                    JarId = data.NextJarId.Value,
                    TotalAmount = data.NextJarData.TotalAmount,
                    DepositCount = data.NextJarData.DepositCount,
                    Deposits = data.NextJarData.Deposits.Select(d => new DepositSummaryResponse
                    {
                        Date = d.DateAsText,
                        DomainId = d.DomainId,
                        AccountNumber = d.AccountNumber,
                        Amount = d.Amount,
                        DocumentNumber = d.DocumentNumber,
                        Address = d.Address
                    })
                };
            }
            else if (data.NextTankData != null)
            {
                response.NextTank = new TankDepositsResponse
                {
                    TankId = data.NextTankId.Value,
                    TankDescription = data.NextTankData.TankInfo.Description,
                    OriginType = data.NextTankData.TankInfo.OriginType,
                    OriginId = data.NextTankData.TankInfo.OriginId,
                    TotalAmount = data.NextTankData.TotalDepositsAmount,
                    DepositCount = data.NextTankData.DepositsCount,
                    Deposits = data.NextTankData.Deposits.Select(d => new TankDepositSummaryResponse
                    {
                        Date = d.DateAsText,
                        Amount = d.Amount,
                        DocumentNumber = d.DocumentNumber,
                        StoreId = d.StoreId,
                        AccountNumber = d.AccountNumber
                    })
                };
            }

            return response;
        }

        public class SearchRequest
        {
            public string? Account { get; set; }
            public string? From { get; set; }
            public string? To { get; set; }
            public string? TransactionId { get; set; }
            public string? State { get; set; }
            public int? InitialIndex { get; set; }
            public int? AmountOfRows { get; set; }
            public string? ExternalId { get; set; }
            public string? Address { get; set; }
        }

        [DataContract(Name = "TransactionSearchResponse")]
        public class TransactionSearchResponse
        {
            [DataMember(Name = "transactions")]
            public IEnumerable<TransactionSearchResult> Transactions { get; set; }

            [DataMember(Name = "totalDepositsAmount")]
            public decimal TotalDepositsAmount { get; set; }

            [DataMember(Name = "totalWithdrawalsAmount")]
            public decimal TotalWithdrawalsAmount { get; set; }

            [DataMember(Name = "transactionsCount")]
            public long TransactionsCount { get; set; }
        }

        [DataContract(Name = "DepositsInNewestJarResponse")]
        public class DepositsInJarResponse
        {
            [DataMember(Name = "totalAmount")]
            public decimal TotalAmount { get; set; }

            [DataMember(Name = "depositCount")]
            public ulong DepositCount { get; set; }

            [DataMember(Name = "deposits")]
            public IEnumerable<DepositSummaryResponse> Deposits { get; set; }
        }

        [DataContract(Name = "DepositSummaryResponse")]
        public class DepositSummaryResponse
        {
            [DataMember(Name = "date")]
            public string Date { get; set; }
            [DataMember(Name = "domainId")]
            public int DomainId { get; set; }
            [DataMember(Name = "accountNumber")]
            public string AccountNumber { get; set; }
            [DataMember(Name = "amount")]
            public decimal Amount { get; set; }
            [DataMember(Name = "documentNumber")]
            public string DocumentNumber { get; set; }
            [DataMember(Name = "address")]
            public string Address { get; set; }
        }

        [DataContract(Name = "JarWithDepositsResponse")]
        public class JarWithDepositsResponse
        {
            [DataMember(Name = "jarId")]
            public long JarId { get; set; }
            [DataMember(Name = "totalAmount")]
            public decimal TotalAmount { get; set; }
            [DataMember(Name = "depositCount")]
            public ulong DepositCount { get; set; }
            [DataMember(Name = "deposits")]
            public IEnumerable<DepositSummaryResponse> Deposits { get; set; }
        }

        [DataContract(Name = "JarComparisonResponse")]
        public class JarComparisonResponse
        {
            [DataMember(Name = "currentJar")]
            public JarWithDepositsResponse CurrentJar { get; set; }
            [DataMember(Name = "originJar")]
            public JarWithDepositsResponse OriginJar { get; set; }
        }

        [DataContract(Name = "JarNextContainerResponse")]
        public class JarNextContainerResponse
        {
            [DataMember(Name = "currentJar")]
            public JarWithDepositsResponse CurrentJar { get; set; }
            [DataMember(Name = "nextJar", EmitDefaultValue = false)]
            public JarWithDepositsResponse NextJar { get; set; }
            [DataMember(Name = "nextTank", EmitDefaultValue = false)]
            public TankDepositsResponse NextTank { get; set; }
        }

        [DataContract(Name = "TankDepositSummaryResponse")]
        public class TankDepositSummaryResponse
        {
            [DataMember(Name = "date")]
            public string Date { get; set; }
            [DataMember(Name = "amount")]
            public decimal Amount { get; set; }
            [DataMember(Name = "documentNumber")]
            public string DocumentNumber { get; set; }
            [DataMember(Name = "storeId")]
            public int StoreId { get; set; }
            [DataMember(Name = "accountNumber")]
            public string AccountNumber { get; set; }
        }

        [DataContract(Name = "TankDepositsResponse")]
        public class TankDepositsResponse
        {
            [DataMember(Name = "tankId")]
            public long TankId { get; set; }
            [DataMember(Name = "tankDescription")]
            public string TankDescription { get; set; }
            [DataMember(Name = "originType")]
            public string OriginType { get; set; }
            [DataMember(Name = "originId")]
            public long? OriginId { get; set; }
            [DataMember(Name = "totalAmount")]
            public decimal TotalAmount { get; set; }
            [DataMember(Name = "depositCount")]
            public ulong DepositCount { get; set; }
            [DataMember(Name = "deposits")]
            public IEnumerable<TankDepositSummaryResponse> Deposits { get; set; }
        }

        [DataContract(Name = "TankComparisonResponse")]
        public class TankComparisonResponse
        {
            [DataMember(Name = "currentTank")]
            public TankDepositsResponse CurrentTank { get; set; }
            [DataMember(Name = "originJar", EmitDefaultValue = false)]
            public JarWithDepositsResponse OriginJar { get; set; }
            [DataMember(Name = "originTank", EmitDefaultValue = false)]
            public TankDepositsResponse OriginTank { get; set; }
        }

        [DataContract(Name = "TankNextContainerResponse")]
        public class TankNextContainerResponse
        {
            [DataMember(Name = "currentTank")]
            public TankDepositsResponse CurrentTank { get; set; }
            [DataMember(Name = "nextTank", EmitDefaultValue = false)]
            public TankDepositsResponse NextTank { get; set; }
            [DataMember(Name = "nextTanker", EmitDefaultValue = false)]
            public TankerDepositsResponse NextTanker { get; set; }
        }

        public class TankVersionComparisonResponse
        {
            public long TankId { get; set; }
            public string TankDescription { get; set; }
            public decimal TotalAmount { get; set; }
            public int Version { get; set; }
            public bool HasNext { get; set; }
            public int? NextTankVersion { get; set; }
            public bool HasPrevious { get; set; }
            public int? PreviousVersion { get; set; }
            public bool IsTankObsolete { get; set; }
            public IEnumerable<TankTransactionSummary> Transactions { get; set; }
            public int DepositCount { get; set; }
            public long? PreviousTankId { get; set; }
            public string PreviousTankDescription { get; set; }
            public decimal? PreviousTankTotalAmount { get; set; }
            public IEnumerable<TankTransactionSummary> PreviousTankTransactions { get; set; }
            public int? PreviousTankDepositCount { get; set; }
        }

        public class TankTransactionSummary
        {
            public long Id { get; set; }
            public string CreatedAt { get; set; }
            public int DomainId { get; set; }
            public string ExternalAtAddress { get; set; }
            public decimal Amount { get; set; }
            public string ConfirmedCurrency { get; set; }
            public decimal ConfirmedAmount { get; set; }
            public string Destination { get; set; }
        }

        public class TankerVersionComparisonResponse
        {
            public long TankerId { get; set; }
            public string TankerDescription { get; set; }
            public decimal TotalAmount { get; set; }
            public int Version { get; set; }
            public bool HasNext { get; set; }
            public int? NextTankerVersion { get; set; }
            public bool HasPrevious { get; set; }
            public int? PreviousTankerVersion { get; set; }
            public bool IsTankerObsolete { get; set; }
            public IEnumerable<TankTransactionSummary> Transactions { get; set; }
            public int DepositCount { get; set; }
            public long? PreviousTankerId { get; set; }
            public string PreviousTankerDescription { get; set; }
            public decimal? PreviousTankerTotalAmount { get; set; }
            public IEnumerable<TankTransactionSummary> PreviousTankerTransactions { get; set; }
            public int? PreviousTankerDepositCount { get; set; }
        }

        [DataContract(Name = "TankerDepositSummaryResponse")]
        public class TankerDepositSummaryResponse
        {
            [DataMember(Name = "date")]
            public string Date { get; set; }
            [DataMember(Name = "amount")]
            public decimal Amount { get; set; }
            [DataMember(Name = "documentNumber")]
            public string DocumentNumber { get; set; }
            [DataMember(Name = "storeId")]
            public int StoreId { get; set; }
            [DataMember(Name = "accountNumber")]
            public string AccountNumber { get; set; }
        }

        [DataContract(Name = "TankerDepositsResponse")]
        public class TankerDepositsResponse
        {
            [DataMember(Name = "tankerId")]
            public long TankerId { get; set; }
            [DataMember(Name = "tankerDescription")]
            public string TankerDescription { get; set; }
            [DataMember(Name = "totalAmount")]
            public decimal TotalAmount { get; set; }
            [DataMember(Name = "depositCount")]
            public ulong DepositCount { get; set; }
            [DataMember(Name = "deposits")]
            public IEnumerable<TankerDepositSummaryResponse> Deposits { get; set; }
        }

        [DataContract(Name = "DepositAmountAndAddress")]
        public class DepositAmountAndAddress
        {
            [DataMember(Name = "amount")]
            public decimal Amount { get; set; }
            [DataMember(Name = "address")]
            public string Address { get; set; }
        }

        [DataContract(Name = "TankerSimpleDepositsResponse")]
        public class TankerSimpleDepositsResponse
        {
            [DataMember(Name = "id")]
            public long Id { get; set; }
            [DataMember(Name = "name")]
            public string Name { get; set; }
            [DataMember(Name = "description")]
            public string Description { get; set; }
            [DataMember(Name = "deposits")]
            public IEnumerable<DepositAmountAndAddress> Deposits { get; set; }
        }

        [DataContract(Name = "DispenserWithdrawalSummaryResponse")]
        public class DispenserWithdrawalSummaryResponse
        {
            [DataMember(Name = "date")]
            public string Date { get; set; }
            [DataMember(Name = "amount")]
            public decimal Amount { get; set; }
            [DataMember(Name = "documentNumber")]
            public string DocumentNumber { get; set; }
            [DataMember(Name = "storeId")]
            public int StoreId { get; set; }
            [DataMember(Name = "accountNumber")]
            public string AccountNumber { get; set; }
        }

        [DataContract(Name = "DispenserWithdrawalsResponse")]
        public class DispenserWithdrawalsResponse
        {
            [DataMember(Name = "dispenserId")]
            public long DispenserId { get; set; }
            [DataMember(Name = "dispenserDescription")]
            public string DispenserDescription { get; set; }
            [DataMember(Name = "totalAmount")]
            public decimal TotalAmount { get; set; }
            [DataMember(Name = "withdrawalCount")]
            public ulong WithdrawalCount { get; set; }
            [DataMember(Name = "withdrawals")]
            public IEnumerable<DispenserWithdrawalSummaryResponse> Withdrawals { get; set; }
        }

        public class DispenserVersionComparisonResponse
        {
            public long DispenserId { get; set; }
            public string Name { get; set; }
            public string DispenserDescription { get; set; }
            public decimal TotalAmount { get; set; }
            public int Version { get; set; }
            public bool HasNext { get; set; }
            public int? NextDispenserVersion { get; set; }
            public bool HasPrevious { get; set; }
            public int? PreviousDispenserVersion { get; set; }
            public bool IsDispenserObsolete { get; set; }
            public IEnumerable<DispenserTransactionSummary> Transactions { get; set; }
            public int WithdrawalCount { get; set; }
            public long? PreviousDispenserId { get; set; }
            public string PreviousName { get; set; }
            public string PreviousDispenserDescription { get; set; }
            public decimal? PreviousDispenserTotalAmount { get; set; }
            public IEnumerable<DispenserTransactionSummary> PreviousDispenserTransactions { get; set; }
            public int? PreviousDispenserWithdrawalCount { get; set; }
        }

        public class DispenserTransactionSummary
        {
            public long Id { get; set; }
            public string CreatedAt { get; set; }
            public int DomainId { get; set; }
            public string AtAddress { get; set; }
            public decimal Amount { get; set; }
            public decimal ReceivedAmount { get; set; }
            public string Destination { get; set; }
        }

        [DataContract(Name = "LastWithdrawalAddressesResponse")]
        public class LastWithdrawalAddressesResponse
        {
            [DataMember(Name = "addresses")]
            public IEnumerable<string> Addresses { get; set; }
        }

        [DataContract(Name = "DailyDomainSummaryResponse")]
        public class DailyDomainSummaryResponse
        {
            [DataMember(Name = "date")]
            public string Date { get; set; }

            [DataMember(Name = "domainId")]
            public int DomainId { get; set; }

            [DataMember(Name = "totalDepositsAmount")]
            public decimal TotalDepositsAmount { get; set; }

            [DataMember(Name = "totalWithdrawalsAmount")]
            public decimal TotalWithdrawalsAmount { get; set; }

            [DataMember(Name = "netTransactionsAmount")]
            public decimal NetTransactionsAmount { get; set; }

            [DataMember(Name = "depositsCount")]
            public ulong DepositsCount { get; set; }

            [DataMember(Name = "withdrawalsCount")]
            public ulong WithdrawalsCount { get; set; }

            [DataMember(Name = "totalTransactionsCount")]
            public ulong TotalTransactionsCount { get; set; }
        }

        [DataContract(Name = "DailyDomainTransactionsResponse")]
        public class DailyDomainTransactionsResponse
        {
            [DataMember(Name = "summaries")]
            public IEnumerable<DailyDomainSummaryResponse> Summaries { get; set; }
        }

        [DataContract(Name = "DailyDepositSummaryItem")]
        public class DailyDepositSummaryItem
        {
            [DataMember(Name = "date")]
            public string Date { get; set; }

            [DataMember(Name = "domainId")]
            public int DomainId { get; set; }

            [DataMember(Name = "transactions")]
            public ulong Transactions { get; set; }

            [DataMember(Name = "totalAmount")]
            public decimal TotalAmount { get; set; }

            [DataMember(Name = "totalAmountUSD")]
            public decimal TotalAmountUSD { get; set; }

            [DataMember(Name = "fxGainLoss")]
            public decimal FxGainLoss { get; set; }
        }

        [DataContract(Name = "DailyDepositsByDomainResponse")]
        public class DailyDepositsByDomainResponse
        {
            [DataMember(Name = "depositsSummary")]
            public IEnumerable<DailyDepositSummaryItem> DepositsSummaries { get; set; }

            [DataMember(Name = "totalAmount")]
            public decimal TotalAmount { get; set; }

            [DataMember(Name = "totalAmountUSD")]
            public decimal TotalAmountUSD { get; set; }
        }

        [DataContract(Name = "DailyDepositDetailItem")]
        public class DailyDepositDetailItem
        {
            [DataMember(Name = "documentNumber")]
            public string DocumentNumber { get; set; }

            [DataMember(Name = "address")]
            public string Address { get; set; }

            [DataMember(Name = "amount")]
            public decimal Amount { get; set; }
        }

        [DataContract(Name = "DailyDepositsDetailResponse")]
        public class DailyDepositsDetailResponse
        {
            [DataMember(Name = "date")]
            public string Date { get; set; }

            [DataMember(Name = "domainId")]
            public int DomainId { get; set; }

            [DataMember(Name = "transactionCount")]
            public int TransactionCount { get; set; }

            [DataMember(Name = "totalAmount")]
            public decimal TotalAmount { get; set; }

            [DataMember(Name = "details")]
            public IEnumerable<DailyDepositDetailItem> Details { get; set; }
        }

        [DataContract(Name = "DailyWithdrawalSummaryItem")]
        public class DailyWithdrawalSummaryItem
        {
            [DataMember(Name = "date")]
            public string Date { get; set; }

            [DataMember(Name = "domainId")]
            public int DomainId { get; set; }

            [DataMember(Name = "transactions")]
            public ulong Transactions { get; set; }

            [DataMember(Name = "totalAmount")]
            public decimal TotalAmount { get; set; }

            [DataMember(Name = "totalAmountUSD")]
            public decimal TotalAmountUSD { get; set; }

            [DataMember(Name = "fxGainLoss")]
            public decimal FxGainLoss { get; set; }
        }

        [DataContract(Name = "DailyWithdrawalsByDomainResponse")]
        public class DailyWithdrawalsByDomainResponse
        {
            [DataMember(Name = "withdrawalsSummary")]
            public IEnumerable<DailyWithdrawalSummaryItem> WithdrawalsSummaries { get; set; }

            [DataMember(Name = "totalAmount")]
            public decimal TotalAmount { get; set; }

            [DataMember(Name = "totalAmountUSD")]
            public decimal TotalAmountUSD { get; set; }
        }

        [DataContract(Name = "DailyWithdrawalDetailItem")]
        public class DailyWithdrawalDetailItem
        {
            [DataMember(Name = "documentNumber")]
            public string DocumentNumber { get; set; }

            [DataMember(Name = "address")]
            public string Address { get; set; }

            [DataMember(Name = "amount")]
            public decimal Amount { get; set; }
        }

        [DataContract(Name = "DailyWithdrawalsDetailResponse")]
        public class DailyWithdrawalsDetailResponse
        {
            [DataMember(Name = "date")]
            public string Date { get; set; }

            [DataMember(Name = "domainId")]
            public int DomainId { get; set; }

            [DataMember(Name = "transactionCount")]
            public int TransactionCount { get; set; }

            [DataMember(Name = "totalAmount")]
            public decimal TotalAmount { get; set; }

            [DataMember(Name = "details")]
            public IEnumerable<DailyWithdrawalDetailItem> Details { get; set; }
        }

        [DataContract(Name = "TankerMonthlyBreakdownResponse")]
        public class TankerMonthlyBreakdownResponse
        {
            [DataMember(Name = "tankerId")]
            public long TankerId { get; set; }
            [DataMember(Name = "tankerName")]
            public string TankerName { get; set; }
            [DataMember(Name = "description")]
            public string Description { get; set; }
            [DataMember(Name = "totalAmount")]
            public decimal TotalAmount { get; set; }
            [DataMember(Name = "monthData")]
            public IEnumerable<MonthData> MonthData { get; set; }
        }

        [DataContract(Name = "MonthData")]
        public class MonthData
        {
            [DataMember(Name = "month")]
            public string Month { get; set; }
            [DataMember(Name = "monthTotal")]
            public decimal MonthTotal { get; set; }
            [DataMember(Name = "tanks", EmitDefaultValue = false)]
            public IEnumerable<TankInMonth> Tanks { get; set; }
            [DataMember(Name = "rootDeposits", EmitDefaultValue = false)]
            public IEnumerable<RootDepositInMonth> RootDeposits { get; set; }
        }

        [DataContract(Name = "TankInMonth")]
        public class TankInMonth
        {
            [DataMember(Name = "id")]
            public long Id { get; set; }
            [DataMember(Name = "name")]
            public string Name { get; set; }
            [DataMember(Name = "status")]
            public string Status { get; set; }
            [DataMember(Name = "amount")]
            public decimal Amount { get; set; }
            [DataMember(Name = "createdAt")]
            public string CreatedAt { get; set; }
        }

        [DataContract(Name = "RootDepositInMonth")]
        public class RootDepositInMonth
        {
            [DataMember(Name = "id")]
            public long Id { get; set; }
            [DataMember(Name = "invoiceId")]
            public string InvoiceId { get; set; }
            [DataMember(Name = "amount")]
            public decimal Amount { get; set; }
            [DataMember(Name = "createdAt")]
            public string CreatedAt { get; set; }
        }
    }
}
