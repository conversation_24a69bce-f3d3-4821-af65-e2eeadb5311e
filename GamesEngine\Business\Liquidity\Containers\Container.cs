﻿using GamesEngine.Finance;
using GamesEngine.MessageQueuing;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Drawing;

namespace GamesEngine.Business.Liquidity.Containers
{
    internal abstract class Container : Objeto
    {
        private string name;
        private string description;
        private int id;
        private DateTime minDate;
        private DateTime maxDate;
        internal decimal Amount { get; private set; }
        internal decimal ReceivedAmount { get; private set; }
        internal string Kind { get; private set; }
        internal Liquid Liquid { get; private set; }
        internal string Type => this.GetType().Name;
        internal DateTime CreatedAt { get; private set; }

        internal GoalAmount GoalAmount { get; set; }

        private Color containerColor = Color.Empty;
        internal string ContainerColor => ColorTranslator.ToHtml(containerColor);

        internal DateTime MinDate
        {
            get
            {
                if (minDate == DateTime.MaxValue)
                    return CalculateMinDate();
                else
                    return minDate;
            }
        }

        internal DateTime MaxDate
        {
            get
            {
                if (maxDate == DateTime.MinValue)
                    return CalculateMaxDate();
                else
                    return maxDate;
            }
        }

        protected Container(int id, string kind, Liquid liquid)
        {
            if (id <= 0) throw new ArgumentNullException(nameof(id));
            if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentNullException(nameof(kind));
            if(liquid == null) throw new ArgumentNullException(nameof(liquid));
            this.id = id;
            Kind = kind;
            this.Liquid = liquid;
            Amount = 0;
            GoalAmount = GoalAmount.EmptyGoalAmount;
        }

        protected Container(int id, string kind,Liquid liquid, string name, string description,DateTime createdDate) : this (id, kind, liquid)
        {
            if(createdDate == DateTime.MinValue) throw new ArgumentNullException(nameof(createdDate));
            this.name = name;//DO NOT VALIDATE, IT CAN BE EMPTY
            this.description = description;//DO NOT VALIDATE, IT CAN BE EMPTY
            this.CreatedAt = createdDate;
        }

        internal int Id => id;

        internal string Name
        {
            get
            {
                return name;
            }
            set
            {
                if (string.IsNullOrWhiteSpace(value)) throw new ArgumentNullException(nameof(name));
                if (value != name)
                {
                    name = value;
                    TankHeaderInfoHasChangeEvent eventMsg = new TankHeaderInfoHasChangeEvent(Id);
                    PlatformMonitor.GetInstance().WhenNewEvent(eventMsg);
                }
            }
        }

        internal string Description
        {
            get
            {
                return description;
            }
            set
            {
                if (string.IsNullOrWhiteSpace(value)) throw new ArgumentNullException(nameof(description));
                if (value != description)
                {
                    description = value;
                    TankHeaderInfoHasChangeEvent eventMsg = new TankHeaderInfoHasChangeEvent(Id);
                    PlatformMonitor.GetInstance().WhenNewEvent(eventMsg);
                }
            }
        }

        protected int version = 1;
        internal int Version => version;

        protected Container previous = null;
        internal Container Previous => previous;
        internal bool HasPrevious => previous != null;

        protected Container next = null;
        internal Container Next => next;
        internal bool HasNext => next != null;

        internal Container FindVersion(int version)
        {
            if (version <= 0) throw new ArgumentNullException(nameof(version));
            
            if (version < this.version)
            {
                return previous?.FindVersion(version);
            }
            else if (version > this.version)
            {
                return next?.FindVersion(version);
            }
            else
            {
                return this;
            }
        }

        internal bool HasVersion(int version)
        {
            if (version <= 0) throw new ArgumentNullException(nameof(version));
            return FindVersion(version) != null;
        }

        protected abstract Container Delegate();

        protected void RecalculateAmount()
        {
            Amount = 0;
            ReceivedAmount = 0;
            if (this is Tank tank)
            {
                foreach (var deposit in tank.ExplandedDeposits)
                {
                    Amount += deposit.Amount;

                    ReceivedAmount += deposit.ConfirmedAmount;
                }

                var currentParent = tank?.Parent;
                while (currentParent != null)
                {
                    currentParent?.RecalculateAmount();
                    currentParent = currentParent.Parent;
                }
            }
            else if (this is Dispenser dispenser)
            {
                foreach (var withdrawal in dispenser.ExplandedWithdrawals)
                {
                    Amount += withdrawal.Amount;
                    ReceivedAmount += withdrawal.ReceivedAmount;
                }

                var currentParent = dispenser?.Parent;
                while (currentParent != null)
                {
                    currentParent?.RecalculateAmount();
                    currentParent = currentParent.Parent;
                }
            }
            else if (this is Jar jar)
            {
                foreach (var deposit in jar.Deposits)
                {
                    var enclosureDeposit = jar.FindEnclosureDeposit(deposit.Id);
                    if (!enclosureDeposit.IsCanceled) { 
                        Amount += deposit.Amount;
                        ReceivedAmount += deposit.ConfirmedAmount;

                    }

                }
            }
            else if (this is Tanker tanker)
            {
                foreach (var _tank in tanker.Tanks)
                {
                    Amount += _tank.Amount;
                    ReceivedAmount +=  _tank.ReceivedAmount;
                }
            }
            else if (this is LegacyJar legacyJar)
            {
                foreach (var deposit in legacyJar.Deposits())
                {
                    var enclosureDeposit = legacyJar.FindEnclosureDeposit(deposit.Id);
                    if (!enclosureDeposit.IsCanceled) { 
                        Amount += deposit.Amount; 
                        ReceivedAmount += deposit.ConfirmedAmount; 
                    }   

                }
            }
            else
            {
                throw new GameEngineException($"The container type '{this.GetType().Name}' does not support recalculating amount.");
            }

            ClearMaxMinDate();
            Liquid.Source.ClearMaxMinDate();
        }

        internal void SetColor(string color)
        {
            this.containerColor = ColorTranslator.FromHtml(color);
        }

        internal void ChangeColor(string color)
        {
            if (color == null) throw new ArgumentNullException(nameof(color));
            this.containerColor = ColorTranslator.FromHtml(color);

            ContainerChangeEvent changeEvent = null;
            switch (this)
            {
                case Tank tank:
                    changeEvent = new TankChangeColorEvent(tank.Id);
                    break;
                case Tanker tanker:
                    changeEvent = new TankerChangeColorEvent(tanker.Id);
                    break;
                case Dispenser dispenser:
                    changeEvent = new DispenserChangeColorEvent(dispenser.Id);
                    break;
                default:
                    throw new GameEngineException($"The container type '{this.GetType().Name}' does not support color change events.");
            }
            PlatformMonitor.GetInstance().WhenNewEvent(changeEvent);
        }
        internal bool ItsSameColor(string color)
        {
            if (color == null) throw new ArgumentNullException(nameof(color));

            Color colorCargado = ColorTranslator.FromHtml(color);
            return this.containerColor.ToArgb() == colorCargado.ToArgb();
        }

        protected void UpdateAmount(decimal amountDelta)
        {
            Amount += amountDelta;
            NotifyChange();
        }

        protected void NotifyChange()
        {
            //Rubicon: TODO emitir una notificación a través de SignalR o el mecanismo de eventos configurado.

        }

        internal void AddGoalAmount(decimal amount, string currencyCode)
        {
            if(amount <= 0) throw new ArgumentNullException(nameof(amount));
            if (string.IsNullOrWhiteSpace(currencyCode)) throw new ArgumentNullException(nameof(currencyCode));
            
            if (GoalAmount.IsEmpty)
            {
                GoalAmount = new GoalAmount();
                GoalAmount.Add(amount, currencyCode);
            }
            else
            {
                GoalAmount.Add(amount, currencyCode);
            }
        }

        internal void ClearGoalAmunt()
        {
            if (GoalAmount.IsEmpty) throw new GameEngineException("");
            GoalAmount.Clear();
        }

        internal decimal ExchangeVariation(decimal actualRate)
        {
            var usdAmount = this.Amount * actualRate;
            var diffrence = usdAmount - this.ReceivedAmount;
            decimal rounded = Math.Round(diffrence, 2);
            return rounded == 0 ? 0.0m : rounded;
        }

        protected abstract DateTime CalculateMinDate();
        
       protected abstract DateTime CalculateMaxDate();

       internal void ClearMaxMinDate()
        {
            maxDate = DateTime.MinValue;
            minDate = DateTime.MaxValue;
            bool itsThePresent = ExecutionContext.Current.ItIsThePresent;
            if (itsThePresent)
            {
                CalculateMaxDate();
                CalculateMinDate();
            }
        }
    }

    public abstract class LiquidityEventMessage : TypedMessage
    {
        internal LiquidityEventMessage(LiquidityMessageType messageType) : base((char)(int)messageType)
        {
        }

        internal LiquidityEventMessage(string message) : base(message)
        {
        }

        public static LiquidityMessageType GetType(string message)
        {
            return (LiquidityMessageType)(int)message[0];
        }
    }

    internal class GoalAmount : Objeto
    {
        internal static GoalAmount EmptyGoalAmount { get; } = new GoalAmount();

        private decimal Amount { get; set; }
        private string CurrencyCode { get; set; }

        internal decimal GetAmount() => Amount;

        internal GoalAmount()
        {
            Amount = 0;
            CurrencyCode = string.Empty;
        }
        internal GoalAmount(decimal amount, string currencyCode)
        {
            if (amount < 0) throw new GameEngineException("The GoalAmount must be greater than zero.");
            if (string.IsNullOrWhiteSpace(currencyCode)) throw new GameEngineException("The GoalAmount CurrencyCode cannot be null or empty.");
            this.Amount = amount;
            this.CurrencyCode = currencyCode;
        }

        internal bool IsEmpty => this == EmptyGoalAmount;

        internal void Clear()
        {
            this.Amount = 0;
            this.CurrencyCode = string.Empty;
        }

        internal void Add(decimal amount, string currencyCode)
        {
            if (amount < 0) throw new GameEngineException("The GoalAmount must be greater than zero.");
            if (string.IsNullOrWhiteSpace(currencyCode)) throw new GameEngineException("The GoalAmount CurrencyCode cannot be null or empty.");
            this.Amount = amount;

            if (!Enum.TryParse(currencyCode, out Currencies.CODES currencyCodeEnum))
            {
                throw new GameEngineException($"The currency code '{currencyCode}' is not valid.");
            }
            this.CurrencyCode = currencyCode;
        }
    }

     internal enum FilterContainerStatus
    {
        READY,
        DISCARDED,
        ALL
    };

    internal enum FilterContainerType
    {
        TRANSACTION,
        CONTAINER,
        ALL
    }

    internal class ParentInfoDto : Objeto
    {
        internal int Id { get; set; }
        internal string Name { get; set; }

        internal ParentInfoDto(int id, string name)
        {
            Id = id;
            Name = name;
        }
    }
}
