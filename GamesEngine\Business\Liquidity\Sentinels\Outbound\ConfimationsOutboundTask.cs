﻿using GamesEngine.Business.Liquidity.Transactions;
using GamesEngine.Settings;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static GamesEngine.Business.Liquidity.Containers.Dispenser;
using static GamesEngine.Business.Liquidity.Containers.Dispenser.DispenserReady;
using static GamesEngine.Business.Liquidity.PaymentEngineDocks;

namespace GamesEngine.Business.Liquidity.Sentinels.Outbound
{
    internal class ConfimationsOutboundTask : OutboundTask
    {
        internal SentinelTasks SentinelOutboundTasks { get; private set; }
        internal CancellationTokenSource Cancellation { get; private set; } = new();

        private ConcurrentDictionary<Withdrawal, PayoutInfo> withdrawalPayoutConfirms = new();
        internal int ConfirmedWithdrawals => withdrawalPayoutConfirms.Count(w => w.Value != null);
        internal int TotalWithdrawals => withdrawalPayoutConfirms.Count;
        internal IEnumerable<Withdrawal> Withdrawals => withdrawalPayoutConfirms.Keys;
        
        internal PaymentEngineDock PaymentEngineDock { get; private set; }

        internal DispenserReady Dispenser { get; private set; }

        internal int DispenserId => Dispenser.Id;

        internal DateTime Created { get; private set; }

        internal ConfimationsOutboundTask(DateTime now , SentinelTasks outboundTasks, DispenserReady dispenserReady, PaymentEngineDock paymentEngineDock)
        {
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));
            if (outboundTasks == null) throw new ArgumentNullException(nameof(outboundTasks));
            if (dispenserReady == null) throw new ArgumentNullException(nameof(dispenserReady));
            if (paymentEngineDock == null) throw new ArgumentNullException(nameof(paymentEngineDock));

            Created = now;
            SentinelOutboundTasks = outboundTasks;
            this.Dispenser = dispenserReady;
            PaymentEngineDock = paymentEngineDock;
        }

        private DateTime runningTaskSince = DateTime.MinValue;
        internal override void StartOutboundTask(bool itIsThePresent, DateTime now)
        {
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));

            runningTaskSince = now;
            if (task == null)
            {   
                task = Task.Run(async () =>
                {
                    if (!withdrawalPayoutConfirms.Any())
                    {
                        foreach (var withdrawal in Dispenser.ExplandedWithdrawals)
                        {
                            if (withdrawal == null) continue;
                            if (withdrawalPayoutConfirms.ContainsKey(withdrawal)) continue;
                            withdrawalPayoutConfirms.TryAdd(withdrawal, null);
                            if (itIsThePresent)
                            {
                                await PaymentManager.ClaimPullPaymentAsync(withdrawal.PullPaymentId, withdrawal.Destination, withdrawal.Amount, Dispenser.Kind);
                            }
                        }
                    }

                    while (withdrawalPayoutConfirms.Any(w => w.Value == null) && !Cancellation.IsCancellationRequested)
                    {
                        await Task.Delay(SentinelTasks.DELAY_SECONDS_PER_CONFIRMATION * 1000);
                        if (DateTime.Now - runningTaskSince >= TimeSpan.FromDays(30)) break;
                    }
                    if (Cancellation.IsCancellationRequested) return;

                    if (Integration.UseKafka && itIsThePresent)
                    {
                        using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, Integration.Kafka.TopicForLiquidityEvents))
                        {
                            List<int> withdrawalConfirms = withdrawalPayoutConfirms.Where(w => w.Value != null).Select(w => w.Key.Id).ToList();
                            var message = new DispatchedDispenserMessage(Dispenser.Kind, Dispenser.Id, DateTime.Now, withdrawalConfirms);
                            buffer.Send(message);
                        }
                    }

                    SentinelOutboundTasks.Detach(this);
                });
            }
        }

        internal void ConfirmWithdrawal(Withdrawal withdrawal, string payoutId, string paymentDockId)
        {
            if (withdrawal == null) throw new ArgumentNullException(nameof(withdrawal));
            if (string.IsNullOrWhiteSpace(payoutId)) throw new ArgumentNullException(nameof(payoutId));
            if (string.IsNullOrWhiteSpace(paymentDockId)) throw new ArgumentNullException(nameof(paymentDockId));
            if (paymentDockId != PaymentEngineDock.Dock) throw new GameEngineException($"The payment dock ID {paymentDockId} does not match the expected dock ID {PaymentEngineDock.Dock}.");

            if (withdrawalPayoutConfirms.ContainsKey(withdrawal))
            {
                withdrawalPayoutConfirms[withdrawal] = new PayoutInfo(payoutId, paymentDockId);
            }
            else
            {
                throw new GameEngineException($"The withdrawal with ID {withdrawal.Id} is not being tracked for confirmation.");
            }
        }

        internal class PayoutInfo
        {
            internal string PayoutId { get; private set; }
            internal string PaymentStoreId { get; private set; }
            internal DateTime ConfirmedAt { get; private set; }
            internal PayoutInfo(string payoutId, string paymentStoreId)
            {
                if (string.IsNullOrWhiteSpace(payoutId)) throw new ArgumentNullException(nameof(payoutId));
                if (string.IsNullOrWhiteSpace(paymentStoreId)) throw new ArgumentNullException(nameof(paymentStoreId));
                PayoutId = payoutId;
                PaymentStoreId = paymentStoreId;
            }
        }
    }
}
