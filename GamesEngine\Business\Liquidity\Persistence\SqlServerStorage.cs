﻿using Elastic.Clients.Elasticsearch.Xpack;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static GamesEngine.Business.Liquidity.Persistence.DataModel;
using Bottle = GamesEngine.Business.Liquidity.Persistence.DataModel.Bottle;
using Dispenser = GamesEngine.Business.Liquidity.Persistence.DataModel.Dispenser;
using Tank = GamesEngine.Business.Liquidity.Persistence.DataModel.Tank;
using Tanker = GamesEngine.Business.Liquidity.Persistence.DataModel.Tanker;

namespace GamesEngine.Business.Liquidity.Persistence
{
    public class SqlServerStorage : IStorage
    {
        private readonly string _connectionString;

        private const string BaseDepositTableName = "Deposit";
        private const string BaseJarTableName = "Jar";
        private const string BaseTankTableName = "Tank";
        private const string BaseTankerTableName = "Tanker";
        private const string BaseJarDetailTableName = "JarDetail";
        private const string BaseTankDetailTableName = "TankDetail";
        private const string BaseTankerDetailTableName = "TankerDetail";
        private const string BaseWithdrawalTableName = "Withdrawal";
        private const string BaseBottleTableName = "Bottle";
        private const string BaseDispenserTableName = "Dispenser";
        private const string BaseBottleDetailTableName = "BottleDetail";
        private const string BaseDispenserDetailTableName = "DispenserDetail";

        public SqlServerStorage(string connectionString)
        {
            _connectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
        }

        private string GetDynamicTableName(string baseName, string kind)
        {
            if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentException("Kind cannot be null or whitespace for dynamic table naming.", nameof(kind));
            
            var sanitizedKind = new string(kind.Where(char.IsLetterOrDigit).ToArray());
            if (string.IsNullOrWhiteSpace(sanitizedKind)) throw new ArgumentException("Sanitized kind results in an empty string.", nameof(kind));
            
            return $"{baseName}_{sanitizedKind}";
        }

        private void ExecuteConditionalCreateTable(string tableName, string createTableStatement)
        {
            string checkTableSql = $@"
                IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES
                               WHERE TABLE_SCHEMA = 'dbo' AND TABLE_NAME = '{tableName}')";

            string fullCommandSql = $"{checkTableSql} BEGIN {createTableStatement} END;";

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = fullCommandSql;
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (SqlException ex)
            {
                Loggers.GetIntance().Db.Error($"SQL Server Error creating table `{tableName}`: {fullCommandSql}", ex);
                ErrorsSender.Send(ex, fullCommandSql);
                throw;
            }
        }

        public void CreateTablesIfNotExists(string kind)
        {
            string depositTableName = GetDynamicTableName(BaseDepositTableName, kind);
            string createDepositTableSql = $@"
                CREATE TABLE {depositTableName} (
                    Id BIGINT PRIMARY KEY,
                    DocumentNumber NVARCHAR(45) NOT NULL,
                    Amount DECIMAL(16, 8) NOT NULL,
                    Date DATETIME2 NOT NULL,
                    Store TINYINT NOT NULL,
                    AccountNumber NVARCHAR(60) NOT NULL,
                    DomainId INT NOT NULL,
                    Address NVARCHAR(255) NOT NULL,
                    Created DATETIME2 NOT NULL
                );";
            ExecuteConditionalCreateTable(depositTableName, createDepositTableSql);

            string jarTableName = GetDynamicTableName(BaseJarTableName, kind);
            string createJarTableSql = $@"
                CREATE TABLE {jarTableName} (
                    Version BIGINT PRIMARY KEY,
                    Description NVARCHAR(MAX) NULL,
                    Created DATETIME2 NOT NULL 
                );";
            ExecuteConditionalCreateTable(jarTableName, createJarTableSql);

            string tankTableName = GetDynamicTableName(BaseTankTableName, kind);
            string createTankTableSql = $@"
                CREATE TABLE {tankTableName} (
                    Id BIGINT PRIMARY KEY,
                    Description NVARCHAR(MAX) NULL,
                    Created DATETIME2 NOT NULL 
                );";
            ExecuteConditionalCreateTable(tankTableName, createTankTableSql);

            string tankerTableName = GetDynamicTableName(BaseTankerTableName, kind);
            string createTankerTableSql = $@"
                CREATE TABLE {tankerTableName} (
                    Id BIGINT PRIMARY KEY,
                    Description NVARCHAR(MAX) NULL,
                    Created DATETIME2 NOT NULL 
                );";
            ExecuteConditionalCreateTable(tankerTableName, createTankerTableSql);

            string jarDetailTableName = GetDynamicTableName(BaseJarDetailTableName, kind);
            string createJarDetailTableSql = $@"
                CREATE TABLE {jarDetailTableName} (
                    JarVersion BIGINT NOT NULL,
                    DepositId BIGINT NOT NULL,
                    Created DATETIME2 NOT NULL
                );";
            ExecuteConditionalCreateTable(jarDetailTableName, createJarDetailTableSql);

            string tankDetailTableName = GetDynamicTableName(BaseTankDetailTableName, kind);
            string createTankDetailTableSql = $@"
                CREATE TABLE {tankDetailTableName} (
                    TankId BIGINT NOT NULL,
                    DepositId BIGINT NOT NULL,
                    Created DATETIME2 NOT NULL
                );";
            ExecuteConditionalCreateTable(tankDetailTableName, createTankDetailTableSql);

            string tankerDetailTableName = GetDynamicTableName(BaseTankerDetailTableName, kind);
            string createTankerDetailTableSql = $@"
                CREATE TABLE {tankerDetailTableName} (
                    TankerId BIGINT NOT NULL,
                    DepositId BIGINT NOT NULL,
                    Created DATETIME2 NOT NULL
                );";
            ExecuteConditionalCreateTable(tankerDetailTableName, createTankerDetailTableSql);

            string withdrawalTableName = GetDynamicTableName(BaseWithdrawalTableName, kind);
            string createWithdrawalTableSql = $@"
                CREATE TABLE {withdrawalTableName} (
                    Id BIGINT PRIMARY KEY,
                    DocumentNumber NVARCHAR(45) NOT NULL,
                    Amount DECIMAL(16, 8) NOT NULL,
                    Date DATETIME2 NOT NULL,
                    Store TINYINT NOT NULL,
                    AccountNumber NVARCHAR(60) NOT NULL,
                    DomainId INT NOT NULL,
                    Address NVARCHAR(255) NOT NULL,
                    Created DATETIME2 NOT NULL
                );";
            ExecuteConditionalCreateTable(withdrawalTableName, createWithdrawalTableSql);

            string bottleTableName = GetDynamicTableName(BaseBottleTableName, kind);
            string createBottleTableSql = $@"
                CREATE TABLE {bottleTableName} (
                    Id BIGINT PRIMARY KEY,
                    Description NVARCHAR(MAX) NULL,
                    Created DATETIME2 NOT NULL 
                );";
            ExecuteConditionalCreateTable(bottleTableName, createBottleTableSql);

            string dispenserTableName = GetDynamicTableName(BaseDispenserTableName, kind);
            string createDispenserTableSql = $@"
                CREATE TABLE {dispenserTableName} (
                    Id BIGINT PRIMARY KEY,
                    Description NVARCHAR(MAX) NULL,
                    Created DATETIME2 NOT NULL,
                    Version INT NOT NULL
                );";
            ExecuteConditionalCreateTable(dispenserTableName, createDispenserTableSql);

            string bottleDetailTableName = GetDynamicTableName(BaseBottleDetailTableName, kind);
            string createBottleDetailTableSql = $@"
                CREATE TABLE {bottleDetailTableName} (
                    WithdrawalId BIGINT NOT NULL,
                    BottleId BIGINT NOT NULL,
                    Created DATETIME2 NOT NULL
                );";
            ExecuteConditionalCreateTable(bottleDetailTableName, createBottleDetailTableSql);

            string dispenserDetailTableName = GetDynamicTableName(BaseDispenserDetailTableName, kind);
            string createDispenserDetailTableSql = $@"
                CREATE TABLE {dispenserDetailTableName} (
                    WithdrawalId BIGINT NOT NULL,
                    DispenserId BIGINT NOT NULL,
                    Created DATETIME2 NOT NULL,
                    DispenserVersion INT NOT NULL
                );";
            ExecuteConditionalCreateTable(dispenserDetailTableName, createDispenserDetailTableSql);
        }

        public void DropAllTables(string kind)
        {
            var baseTableNames = new List<string>
            {
                BaseDepositTableName,
                BaseJarTableName,
                BaseTankTableName,
                BaseTankerTableName,
                BaseJarDetailTableName,
                BaseTankDetailTableName,
                BaseTankerDetailTableName,
                BaseWithdrawalTableName,
                BaseBottleTableName,
                BaseDispenserTableName,
                BaseBottleDetailTableName,
                BaseDispenserDetailTableName
            };

            foreach (var baseName in baseTableNames)
            {
                string tableName = GetDynamicTableName(baseName, kind);
                string dropSql = $"IF OBJECT_ID('dbo.{tableName}', 'U') IS NOT NULL DROP TABLE dbo.{tableName};";
                try
                {
                    using (var connection = new SqlConnection(_connectionString))
                    {
                        connection.Open();
                        using (var command = connection.CreateCommand())
                        {
                            command.CommandText = dropSql;
                            command.ExecuteNonQuery();
                        }
                    }
                }
                catch (SqlException ex)
                {
                    Loggers.GetIntance().Db.Error($"SQL Server Error executing non-query: {dropSql}", ex);
                    ErrorsSender.Send(ex, dropSql);
                    throw;
                }
            }
        }

        public void CreateDeposit(string kind, Deposit deposit)
        {
            string depositTableName = GetDynamicTableName(BaseDepositTableName, kind);
            string insertSql = $@"
                INSERT INTO {depositTableName}
                (Id, DocumentNumber, Amount, Date, Store, AccountNumber, DomainId, Address, Created)
                VALUES
                (@Id, @DocumentNumber, @Amount, @Date, @Store, @AccountNumber, @DomainId, @Address, @Created);";

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    using (var command = new SqlCommand(insertSql, connection))
                    {
                        command.Parameters.Add("@Id", SqlDbType.BigInt).Value = deposit.Id;
                        command.Parameters.AddWithValue("@DocumentNumber", deposit.DocumentNumber);
                        command.Parameters.AddWithValue("@Amount", deposit.Amount);
                        command.Parameters.AddWithValue("@Date", deposit.Date);
                        command.Parameters.Add("@Store", SqlDbType.TinyInt).Value = deposit.StoreId;
                        command.Parameters.AddWithValue("@AccountNumber", deposit.AccountNumber);
                        command.Parameters.Add("@DomainId", SqlDbType.Int).Value = deposit.DomainId;
                        command.Parameters.AddWithValue("@Address", deposit.Address);
                        command.Parameters.AddWithValue("@Created", deposit.Created);
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (SqlException ex)
            {
                Loggers.GetIntance().Db.Error($"SQL Server Error inserting transactions: {insertSql}", ex);
                ErrorsSender.Send(ex, insertSql);
                throw;
            }
        }

        public void CreateJar(string kind, long version, string description, DateTime created, long? previousJar)
        {
            string jarTableName = GetDynamicTableName(BaseJarTableName, kind);
            string insertSql = $"INSERT INTO {jarTableName} (Version, Description, Created) VALUES (@Version, @Description, @Created);";

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    using (var insertCmd = new SqlCommand(insertSql, connection))
                    {
                        insertCmd.Parameters.Add("@Version", SqlDbType.BigInt).Value = version;
                        insertCmd.Parameters.AddWithValue("@Description", (object)description ?? DBNull.Value);
                        insertCmd.Parameters.Add("@Created", SqlDbType.DateTime2).Value = created;
                        insertCmd.ExecuteNonQuery();
                    }
                }
            }
            catch (SqlException ex)
            {
                Loggers.GetIntance().Db.Error($"SQLServer Error in GetOrCreateJar (Desc: {description}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(CreateJar)} (Desc: {description})");
                throw;
            }
        }

        public void CreateTank(string kind, Tank tank)
        {
            string tankTableName = GetDynamicTableName(BaseTankTableName, kind);
            string insertSql = $"INSERT INTO {tankTableName} (Id, Description, Created) VALUES (@Id, @Description, @Created);";
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    using (var insertCmd = new SqlCommand(insertSql, connection))
                    {
                        insertCmd.Parameters.Add("@Id", SqlDbType.BigInt).Value = tank.Id;
                        insertCmd.Parameters.AddWithValue("@Description", (object)tank.Description ?? DBNull.Value);
                        insertCmd.Parameters.Add("@Created", SqlDbType.DateTime2).Value = tank.Created;
                        insertCmd.ExecuteNonQuery();
                    }
                }
            }
            catch (SqlException ex)
            {
                Loggers.GetIntance().Db.Error($"SQLServer Error in GetOrCreateTank (Desc: {tank.Description}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(CreateTank)} (Desc: {tank.Description})");
                throw;
            }
        }

        public void CreateTanker(string kind, Tanker tanker)
        {
            string tankerTableName = GetDynamicTableName(BaseTankerTableName, kind);
            string insertSql = $"INSERT INTO {tankerTableName} (Id, Description, Created) VALUES (@Id, @Description, @Created);";
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    using (var insertCmd = new SqlCommand(insertSql, connection))
                    {
                        insertCmd.Parameters.Add("@Id", SqlDbType.BigInt).Value = tanker.Id;
                        insertCmd.Parameters.AddWithValue("@Description", (object)tanker.Description ?? DBNull.Value);
                        insertCmd.Parameters.Add("@Created", SqlDbType.DateTime2).Value = tanker.Created;
                        insertCmd.ExecuteNonQuery();
                    }
                }
            }
            catch (SqlException ex)
            {
                Loggers.GetIntance().Db.Error($"SQLServer Error in GetOrCreateTanker (Desc: {tanker.Description}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(CreateTanker)} (Desc: {tanker.Description})");
                throw;
            }
        }

        public void CreateJarDetailIfNotExists(string kind, long jarVersion, long depositId, DateTime created)
        {
            string jarDetailTableName = GetDynamicTableName(BaseJarDetailTableName, kind);
            string selectSql = $"SELECT COUNT(1) FROM {jarDetailTableName} WHERE JarVersion = @JarVersion AND DepositId = @DepositId;";
            string insertSql = $"INSERT INTO {jarDetailTableName} (JarVersion, DepositId, Created) VALUES (@JarVersion, @DepositId, @Created);";
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    bool exists;
                    using (var selectCmd = new SqlCommand(selectSql, connection))
                    {
                        selectCmd.Parameters.Add("@JarVersion", SqlDbType.BigInt).Value = jarVersion;
                        selectCmd.Parameters.Add("@DepositId", SqlDbType.BigInt).Value = depositId;
                        exists = (int)selectCmd.ExecuteScalar() > 0;
                    }

                    if (!exists)
                    {
                        using (var insertCmd = new SqlCommand(insertSql, connection))
                        {
                            insertCmd.Parameters.Add("@JarVersion", SqlDbType.BigInt).Value = jarVersion;
                            insertCmd.Parameters.Add("@DepositId", SqlDbType.BigInt).Value = depositId;
                            insertCmd.Parameters.Add("@Created", SqlDbType.DateTime2).Value = created;
                            insertCmd.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (SqlException ex)
            {
                Loggers.GetIntance().Db.Error($"SQLServer Error in CreateJarDetailIfNotExists (JarVersion: {jarVersion}, DepositId: {depositId}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(CreateJarDetailIfNotExists)} (JarVersion: {jarVersion}, DepositId: {depositId})");
                throw;
            }
        }

        public void CreateTankDetailIfNotExists(string kind, long tankId, long depositId, DateTime created)
        {
            string tankDetailTableName = GetDynamicTableName(BaseTankDetailTableName, kind);
            string selectSql = $"SELECT COUNT(1) FROM {tankDetailTableName} WHERE TankId = @TankId AND DepositId = @DepositId;";
            string insertSql = $"INSERT INTO {tankDetailTableName} (TankId, DepositId, Created) VALUES (@TankId, @DepositId, @Created);";
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    bool exists;
                    using (var selectCmd = new SqlCommand(selectSql, connection))
                    {
                        selectCmd.Parameters.Add("@TankId", SqlDbType.BigInt).Value = tankId;
                        selectCmd.Parameters.Add("@DepositId", SqlDbType.BigInt).Value = depositId;
                        exists = (int)selectCmd.ExecuteScalar() > 0;
                    }

                    if (!exists)
                    {
                        using (var insertCmd = new SqlCommand(insertSql, connection))
                        {
                            insertCmd.Parameters.Add("@TankId", SqlDbType.BigInt).Value = tankId;
                            insertCmd.Parameters.Add("@DepositId", SqlDbType.BigInt).Value = depositId;
                            insertCmd.Parameters.Add("@Created", SqlDbType.DateTime2).Value = created;
                            insertCmd.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (SqlException ex)
            {
                Loggers.GetIntance().Db.Error($"SQLServer Error in CreateTankDetailIfNotExists (TankId: {tankId}, DepositId: {depositId}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(CreateTankDetailIfNotExists)} (TankId: {tankId}, DepositId: {depositId})");
                throw;
            }
        }

        public void CreateTankDetails(string kind, long tankId, IEnumerable<int> depositIds, DateTime created)
        {
            if (depositIds == null || !depositIds.Any())
            {
                return;
            }

            string tankDetailTableName = GetDynamicTableName(BaseTankDetailTableName, kind);

            var dt = new DataTable();
            dt.Columns.Add("TankId", typeof(long));
            dt.Columns.Add("DepositId", typeof(long));
            dt.Columns.Add("Created", typeof(DateTime));

            foreach (var depositId in depositIds)
            {
                dt.Rows.Add(tankId, depositId, created);
            }

            if (dt.Rows.Count == 0) return;
            string tempTableName = $"#TankDetails_{Guid.NewGuid():N}";

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = $@"
                            CREATE TABLE {tempTableName} (
                                TankId BIGINT NOT NULL,
                                DepositId BIGINT NOT NULL,
                                Created DATETIME2 NOT NULL,
                                PRIMARY KEY (TankId, DepositId)
                            );";
                        command.ExecuteNonQuery();
                    }

                    using (var bulkCopy = new SqlBulkCopy(connection))
                    {
                        bulkCopy.DestinationTableName = tempTableName;
                        bulkCopy.WriteToServer(dt);
                    }

                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = $@"
                            INSERT INTO {tankDetailTableName} (TankId, DepositId, Created)
                            SELECT s.TankId, s.DepositId, s.Created
                            FROM {tempTableName} s
                            LEFT JOIN {tankDetailTableName} t ON s.TankId = t.TankId AND s.DepositId = t.DepositId
                            WHERE t.TankId IS NULL; -- Only insert if not already present
                        ";
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (SqlException ex)
            {
                Loggers.GetIntance().Db.Error($"SQL Server Error in {nameof(CreateTankDetails)} (TankId: {tankId}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(CreateTankDetails)} (TankId: {tankId})");
                throw;
            }
        }

        public void CreateTankerDetailIfNotExists(string kind, long tankerId, long depositId, DateTime created)
        {
            string tankerDetailTableName = GetDynamicTableName(BaseTankerDetailTableName, kind);
            string selectSql = $"SELECT COUNT(1) FROM {tankerDetailTableName} WHERE TankerId = @TankerId AND DepositId = @DepositId;";
            string insertSql = $"INSERT INTO {tankerDetailTableName} (TankerId, DepositId, Created) VALUES (@TankerId, @DepositId, @Created);";
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    bool exists;
                    using (var selectCmd = new SqlCommand(selectSql, connection))
                    {
                        selectCmd.Parameters.Add("@TankerId", SqlDbType.BigInt).Value = tankerId;
                        selectCmd.Parameters.Add("@DepositId", SqlDbType.BigInt).Value = depositId;
                        exists = (int)selectCmd.ExecuteScalar() > 0;
                    }

                    if (!exists)
                    {
                        using (var insertCmd = new SqlCommand(insertSql, connection))
                        {
                            insertCmd.Parameters.Add("@TankerId", SqlDbType.BigInt).Value = tankerId;
                            insertCmd.Parameters.Add("@DepositId", SqlDbType.BigInt).Value = depositId;
                            insertCmd.Parameters.Add("@Created", SqlDbType.DateTime2).Value = created;
                            insertCmd.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (SqlException ex)
            {
                Loggers.GetIntance().Db.Error($"SQLServer Error in {nameof(CreateTankerDetailIfNotExists)} (TankerId: {tankerId}, DepositId: {depositId}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(CreateTankerDetailIfNotExists)} (TankerId: {tankerId}, DepositId: {depositId})");
                throw;
            }
        }

        public List<Deposit> DepositsInNewestJar(string kind, DateTime startDate, DateTime endDate, string accountNumber = null)
        {
            var deposits = new List<Deposit>();
            long? newestJarVersionByVersion = null;

            string jarTableName = GetDynamicTableName(BaseJarTableName, kind);
            string depositTableName = GetDynamicTableName(BaseDepositTableName, kind);
            string jarDetailTableName = GetDynamicTableName(BaseJarDetailTableName, kind);

            string getNewestJarVersionSql = $"SELECT TOP 1 Version FROM {jarTableName} ORDER BY Version DESC, Created DESC;";

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    using (var jarCmd = new SqlCommand(getNewestJarVersionSql, connection))
                    {
                        var result = jarCmd.ExecuteScalar();
                        if (result != null && result != DBNull.Value)
                        {
                            newestJarVersionByVersion = Convert.ToInt64(result);
                        }
                    }

                    if (!newestJarVersionByVersion.HasValue)
                    {
                        Loggers.GetIntance().Db.Debug("No Jars found, cannot retrieve deposits for the newest jar by version.");
                        return deposits;
                    }

                    var selectDepositsSqlBuilder = new StringBuilder($@"
                        SELECT
                            d.Id, d.DocumentNumber, d.Amount, d.Date, d.Store, d.AccountNumber, d.DomainId, d.Address, d.Created
                        FROM
                            {depositTableName} d
                        INNER JOIN
                            {jarDetailTableName} jd ON d.Id = jd.DepositId
                        WHERE
                            jd.JarVersion = @NewestJarVersion
                            AND d.Date >= @StartDate AND d.Date <= @EndDate");

                    if (!string.IsNullOrEmpty(accountNumber))
                    {
                        selectDepositsSqlBuilder.Append(" AND d.AccountNumber = @AccountNumber");
                    }
                    selectDepositsSqlBuilder.Append(";");

                    string selectDepositsSql = selectDepositsSqlBuilder.ToString();

                    using (var depositCmd = new SqlCommand(selectDepositsSql, connection))
                    {
                        depositCmd.Parameters.Add("@NewestJarVersion", SqlDbType.BigInt).Value = newestJarVersionByVersion.Value;
                        depositCmd.Parameters.Add("@StartDate", SqlDbType.DateTime2).Value = startDate;
                        depositCmd.Parameters.Add("@EndDate", SqlDbType.DateTime2).Value = endDate;
                        if (!string.IsNullOrEmpty(accountNumber))
                        {
                            depositCmd.Parameters.AddWithValue("@AccountNumber", accountNumber);
                        }

                        using (var reader = depositCmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var deposit = new Deposit
                                {
                                    Id = reader.GetInt64(reader.GetOrdinal("Id")),
                                    DocumentNumber = reader.GetString(reader.GetOrdinal("DocumentNumber")),
                                    Amount = reader.GetDecimal(reader.GetOrdinal("Amount")),
                                    Date = reader.GetDateTime(reader.GetOrdinal("Date")),
                                    StoreId = reader.GetByte(reader.GetOrdinal("Store")),
                                    AccountNumber = reader.GetString(reader.GetOrdinal("AccountNumber")),
                                    DomainId = reader.GetInt32(reader.GetOrdinal("DomainId")),
                                    Address = reader.GetString(reader.GetOrdinal("Address")),
                                    Created = reader.GetDateTime(reader.GetOrdinal("Created"))
                                };
                                deposits.Add(deposit);
                            }
                        }
                    }
                }
            }
            catch (SqlException ex)
            {
                string accNumContext = string.IsNullOrEmpty(accountNumber) ? "null" : accountNumber;
                string errorContext = newestJarVersionByVersion.HasValue ? $"newestJarVersionByVersion: {newestJarVersionByVersion.Value}" : "newestJarVersionByVersion: null";
                Loggers.GetIntance().Db.Error($"SQL Server Error in {nameof(DepositsInNewestJar)} ({errorContext}, startDate: {startDate}, endDate: {endDate}, accountNumber: {accNumContext}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(DepositsInNewestJar)}", $"newestJarVersionByVersion {newestJarVersionByVersion}, startDate {startDate}, endDate {endDate}, accountNumber {accNumContext}");
                throw;
            }
            return deposits;
        }

        public TankWithDeposits TankAndAllItsDeposits(string kind, long tankId)
        {
            var result = new TankWithDeposits();
            bool tankInfoSet = false;

            string tankTableName = GetDynamicTableName(BaseTankTableName, kind);
            string tankDetailTableName = GetDynamicTableName(BaseTankDetailTableName, kind);
            string depositTableName = GetDynamicTableName(BaseDepositTableName, kind);

            string selectSql = $@"
                SELECT
                    t.Id AS TankId, t.Description AS TankDescription, t.Created AS TankCreated,
                    d.Id AS DepositId, d.DocumentNumber, d.Amount, d.Date, d.Store, 
                    d.AccountNumber, d.DomainId, d.Address, d.Created AS DepositCreated
                FROM
                    {tankTableName} t
                LEFT JOIN
                    {tankDetailTableName} td ON t.Id = td.TankId
                LEFT JOIN
                    {depositTableName} d ON td.DepositId = d.Id
                WHERE
                    t.Id = @TankId;";

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    using (var command = new SqlCommand(selectSql, connection))
                    {
                        command.Parameters.Add("@TankId", SqlDbType.BigInt).Value = tankId;

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                if (!tankInfoSet)
                                {
                                    result.TankInfo = new Tank
                                    {
                                        Id = reader.GetInt64(reader.GetOrdinal("TankId")),
                                        Description = reader.IsDBNull(reader.GetOrdinal("TankDescription")) ? null : reader.GetString(reader.GetOrdinal("TankDescription")),
                                        Created = reader.GetDateTime(reader.GetOrdinal("TankCreated"))
                                    };
                                    tankInfoSet = true;
                                }

                                if (!reader.IsDBNull(reader.GetOrdinal("DepositId")))
                                {
                                    result.Deposits.Add(new Deposit
                                    {
                                        Id = reader.GetInt64(reader.GetOrdinal("DepositId")),
                                        DocumentNumber = reader.GetString(reader.GetOrdinal("DocumentNumber")),
                                        Amount = reader.GetDecimal(reader.GetOrdinal("Amount")),
                                        Date = reader.GetDateTime(reader.GetOrdinal("Date")),
                                        StoreId = reader.GetByte(reader.GetOrdinal("Store")),
                                        AccountNumber = reader.GetString(reader.GetOrdinal("AccountNumber")),
                                        DomainId = reader.GetInt32(reader.GetOrdinal("DomainId")),
                                        Address = reader.GetString(reader.GetOrdinal("Address")),
                                        Created = reader.GetDateTime(reader.GetOrdinal("DepositCreated"))
                                    });
                                }
                            }
                        }
                    }
                }

                if (result.TankInfo == null)
                {
                    return null;
                }
                return result;
            }
            catch (SqlException ex)
            {
                Loggers.GetIntance().Db.Error($"SQL Server Error in {nameof(TankAndAllItsDeposits)} (TankId: {tankId}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(TankAndAllItsDeposits)}", $"TankId {tankId}");
                throw;
            }
        }

        public TankerWithDeposits TankerAndAllItsDeposits(string kind, long tankerId)
        {
            var result = new TankerWithDeposits();
            bool tankerInfoSet = false;

            string tankerTableName = GetDynamicTableName(BaseTankerTableName, kind);
            string tankerDetailTableName = GetDynamicTableName(BaseTankerDetailTableName, kind);
            string depositTableName = GetDynamicTableName(BaseDepositTableName, kind);

            string selectSql = $@"
                SELECT
                    t.Id AS TankerId, t.Name AS TankerName, t.Description AS TankerDescription, t.Created AS TankerCreated,
                    d.Id AS DepositId, d.DocumentNumber, d.Amount, d.Date, d.Store,
                    d.AccountNumber, d.DomainId, d.Address, d.Created AS DepositCreated
                FROM
                    {tankerTableName} t
                LEFT JOIN
                    {tankerDetailTableName} td ON t.Id = td.TankerId
                LEFT JOIN
                    {depositTableName} d ON td.DepositId = d.Id
                WHERE
                    t.Id = @TankerId;";

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    using (var command = new SqlCommand(selectSql, connection))
                    {
                        command.Parameters.Add("@TankerId", SqlDbType.BigInt).Value = tankerId;

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                if (!tankerInfoSet)
                                {
                                    result.TankerInfo = new Tanker
                                    {
                                        Id = reader.GetInt64(reader.GetOrdinal("TankerId")),
                                        Name = reader.IsDBNull(reader.GetOrdinal("TankerName")) ? null : reader.GetString(reader.GetOrdinal("TankerName")),
                                        Description = reader.IsDBNull(reader.GetOrdinal("TankerDescription")) ? null : reader.GetString(reader.GetOrdinal("TankerDescription")),
                                        Created = reader.GetDateTime(reader.GetOrdinal("TankerCreated"))
                                    };
                                    tankerInfoSet = true;
                                }

                                if (!reader.IsDBNull(reader.GetOrdinal("DepositId")))
                                {
                                    result.Deposits.Add(new Deposit
                                    {
                                        Id = reader.GetInt64(reader.GetOrdinal("DepositId")),
                                        DocumentNumber = reader.GetString(reader.GetOrdinal("DocumentNumber")),
                                        Amount = reader.GetDecimal(reader.GetOrdinal("Amount")),
                                        Date = reader.GetDateTime(reader.GetOrdinal("Date")),
                                        StoreId = reader.GetByte(reader.GetOrdinal("Store")),
                                        AccountNumber = reader.GetString(reader.GetOrdinal("AccountNumber")),
                                        DomainId = reader.GetInt32(reader.GetOrdinal("DomainId")),
                                        Address = reader.GetString(reader.GetOrdinal("Address")),
                                        Created = reader.GetDateTime(reader.GetOrdinal("DepositCreated"))
                                    });
                                }
                            }
                        }
                    }
                }

                if (result.TankerInfo == null)
                {
                    return null;
                }
                return result;
            }
            catch (SqlException ex)
            {
                Loggers.GetIntance().Db.Error($"SQL Server Error in {nameof(TankerAndAllItsDeposits)} (TankerId: {tankerId}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(TankerAndAllItsDeposits)}", $"TankerId {tankerId}");
                throw;
            }
        }

        public DispenserWithWithdrawals DispenserAndAllItsWithdrawals(string kind, long dispenserId)
        {
            var result = new DispenserWithWithdrawals();
            bool dispenserInfoSet = false;

            string dispenserTableName = GetDynamicTableName(BaseDispenserTableName, kind);
            string dispenserDetailTableName = GetDynamicTableName(BaseDispenserDetailTableName, kind);
            string withdrawalTableName = GetDynamicTableName(BaseWithdrawalTableName, kind);

            string selectSql = $@"
                SELECT
                    d.Id AS DispenserId, d.Description AS DispenserDescription, d.Created AS DispenserCreated,
                    w.Id AS WithdrawalId, w.DocumentNumber, w.Amount, w.Date, w.Store,
                    w.AccountNumber, w.DomainId, w.Address, w.Created AS WithdrawalCreated
                FROM
                    {dispenserTableName} d
                LEFT JOIN
                    {dispenserDetailTableName} dd ON d.Id = dd.DispenserId
                LEFT JOIN
                    {withdrawalTableName} w ON dd.WithdrawalId = w.Id
                WHERE
                    d.Id = @DispenserId;";

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    using (var command = new SqlCommand(selectSql, connection))
                    {
                        command.Parameters.Add("@DispenserId", SqlDbType.BigInt).Value = dispenserId;

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                if (!dispenserInfoSet)
                                {
                                    result.DispenserInfo = new Dispenser
                                    {
                                        Id = reader.GetInt64(reader.GetOrdinal("DispenserId")),
                                        Description = reader.IsDBNull(reader.GetOrdinal("DispenserDescription")) ? null : reader.GetString(reader.GetOrdinal("DispenserDescription")),
                                        Created = reader.GetDateTime(reader.GetOrdinal("DispenserCreated"))
                                    };
                                    dispenserInfoSet = true;
                                }

                                if (!reader.IsDBNull(reader.GetOrdinal("WithdrawalId")))
                                {
                                    result.Withdrawals.Add(new Withdrawal
                                    {
                                        Id = reader.GetInt64(reader.GetOrdinal("WithdrawalId")),
                                        DocumentNumber = reader.GetString(reader.GetOrdinal("DocumentNumber")),
                                        Amount = reader.GetDecimal(reader.GetOrdinal("Amount")),
                                        Date = reader.GetDateTime(reader.GetOrdinal("Date")),
                                        StoreId = reader.GetByte(reader.GetOrdinal("Store")),
                                        AccountNumber = reader.GetString(reader.GetOrdinal("AccountNumber")),
                                        DomainId = reader.GetInt32(reader.GetOrdinal("DomainId")),
                                        Address = reader.GetString(reader.GetOrdinal("Address")),
                                        Created = reader.GetDateTime(reader.GetOrdinal("WithdrawalCreated"))
                                    });
                                }
                            }
                        }
                    }
                }

                if (result.DispenserInfo == null)
                {
                    return null;
                }
                return result;
            }
            catch (SqlException ex)
            {
                Loggers.GetIntance().Db.Error($"SQL Server Error in DispenserAndAllItsWithdrawals (DispenserId: {dispenserId}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(DispenserAndAllItsWithdrawals)}", $"DispenserId {dispenserId}");
                throw;
            }
        }

        public void CreateWithdrawal(string kind, Withdrawal w)
        {
            string withdrawalTableName = GetDynamicTableName(BaseWithdrawalTableName, kind);
            string insertSql = $@"
                INSERT INTO {withdrawalTableName}
                (Id, DocumentNumber, Amount, Date, Store, AccountNumber, DomainId, Address, Created)
                VALUES
                (@Id, @DocumentNumber, @Amount, @Date, @Store, @AccountNumber, @DomainId, @Address, @Created);";
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    using (var insertCmd = new SqlCommand(insertSql, connection))
                    {
                        insertCmd.Parameters.Add("@Id", SqlDbType.BigInt).Value = w.Id;
                        insertCmd.Parameters.AddWithValue("@DocumentNumber", w.DocumentNumber);
                        insertCmd.Parameters.AddWithValue("@Amount", w.Amount);
                        insertCmd.Parameters.AddWithValue("@Date", w.Date);
                        insertCmd.Parameters.Add("@Store", SqlDbType.TinyInt).Value = w.StoreId;
                        insertCmd.Parameters.AddWithValue("@AccountNumber", w.AccountNumber);
                        insertCmd.Parameters.Add("@DomainId", SqlDbType.Int).Value = w.DomainId;
                        insertCmd.Parameters.AddWithValue("@Address", w.Address);
                        insertCmd.Parameters.AddWithValue("@Created", w.Created);
                        insertCmd.ExecuteNonQuery();
                    }
                }
            }
            catch (SqlException ex)
            {
                Loggers.GetIntance().Db.Error($"SQL Server Error in GetOrCreateWithdrawal: {ex.Message}", ex);
                ErrorsSender.Send(ex, "GetOrCreateWithdrawal");
                throw;
            }
        }

        public void CreateBottle(string kind, Bottle b)
        {
            string bottleTableName = GetDynamicTableName(BaseBottleTableName, kind);
            string insertSql = $@"
                INSERT INTO {bottleTableName}
                (Id, Description, Created)
                VALUES
                (@Id, @Description, @Created);";
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    using (var insertCmd = new SqlCommand(insertSql, connection))
                    {
                        insertCmd.Parameters.Add("@Id", SqlDbType.BigInt).Value = b.Id;
                        insertCmd.Parameters.AddWithValue("@Description", b.Description ?? (object)DBNull.Value);
                        insertCmd.Parameters.AddWithValue("@Created", b.Created);
                        insertCmd.ExecuteNonQuery();
                    }
                }
            }
            catch (SqlException ex)
            {
                Loggers.GetIntance().Db.Error($"SQL Server Error in GetOrCreateBottle: {ex.Message}", ex);
                ErrorsSender.Send(ex, "GetOrCreateBottle");
                throw;
            }
        }

        public void CreateDispenser(string kind, Dispenser d)
        {
            string dispenserTableName = GetDynamicTableName(BaseDispenserTableName, kind);
            string insertSql = $@"
                INSERT INTO {dispenserTableName}
                (Id, Description, Created, Version)
                VALUES
                (@Id, @Description, @Created, @Version);";
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    using (var insertCmd = new SqlCommand(insertSql, connection))
                    {
                        insertCmd.Parameters.Add("@Id", SqlDbType.BigInt).Value = d.Id;
                        insertCmd.Parameters.AddWithValue("@Description", d.Description ?? (object)DBNull.Value);
                        insertCmd.Parameters.AddWithValue("@Created", d.Created);
                        insertCmd.Parameters.AddWithValue("@Version", d.Version);
                        insertCmd.ExecuteNonQuery();
                    }
                }
            }
            catch (SqlException ex)
            {
                Loggers.GetIntance().Db.Error($"SQL Server Error in GetOrCreateDispenser: {ex.Message}", ex);
                ErrorsSender.Send(ex, "GetOrCreateDispenser");
                throw;
            }
        }

        public void CreateBottleDetailIfNotExists(string kind, long withdrawalId, long bottleId, DateTime created)
        {
            string bottleDetailTableName = GetDynamicTableName(BaseBottleDetailTableName, kind);
            string selectSql = $"SELECT COUNT(1) FROM {bottleDetailTableName} WHERE WithdrawalId = @WithdrawalId AND BottleId = @BottleId;";
            string insertSql = $"INSERT INTO {bottleDetailTableName} (WithdrawalId, BottleId, Created) VALUES (@WithdrawalId, @BottleId, @Created);";
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    bool exists;
                    using (var selectCmd = new SqlCommand(selectSql, connection))
                    {
                        selectCmd.Parameters.AddWithValue("@WithdrawalId", withdrawalId);
                        selectCmd.Parameters.AddWithValue("@BottleId", bottleId);
                        exists = (int)selectCmd.ExecuteScalar() > 0;
                    }
                    if (!exists)
                    {
                        using (var insertCmd = new SqlCommand(insertSql, connection))
                        {
                            insertCmd.Parameters.AddWithValue("@WithdrawalId", withdrawalId);
                            insertCmd.Parameters.AddWithValue("@BottleId", bottleId);
                            insertCmd.Parameters.AddWithValue("@Created", created);
                            insertCmd.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (SqlException ex)
            {
                Loggers.GetIntance().Db.Error($"SQL Server Error in CreateBottleDetailIfNotExists: {ex.Message}", ex);
                ErrorsSender.Send(ex, "CreateBottleDetailIfNotExists");
                throw;
            }
        }

        public void CreateDispenserDetailIfNotExists(string kind, long withdrawalId, long dispenserId, DateTime created, int dispenserVersion)
        {
            string dispenserDetailTableName = GetDynamicTableName(BaseDispenserDetailTableName, kind);
            string selectSql = $"SELECT COUNT(1) FROM {dispenserDetailTableName} WHERE WithdrawalId = @WithdrawalId AND DispenserId = @DispenserId;";
            string insertSql = $"INSERT INTO {dispenserDetailTableName} (WithdrawalId, DispenserId, Created, DispenserVersion) VALUES (@WithdrawalId, @DispenserId, @Created, @DispenserVersion);";
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    bool exists;
                    using (var selectCmd = new SqlCommand(selectSql, connection))
                    {
                        selectCmd.Parameters.AddWithValue("@WithdrawalId", withdrawalId);
                        selectCmd.Parameters.AddWithValue("@DispenserId", dispenserId);
                        exists = (int)selectCmd.ExecuteScalar() > 0;
                    }
                    if (!exists)
                    {
                        using (var insertCmd = new SqlCommand(insertSql, connection))
                        {
                            insertCmd.Parameters.AddWithValue("@WithdrawalId", withdrawalId);
                            insertCmd.Parameters.AddWithValue("@DispenserId", dispenserId);
                            insertCmd.Parameters.AddWithValue("@Created", created);
                            insertCmd.Parameters.AddWithValue("@DispenserVersion", dispenserVersion);
                            insertCmd.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (SqlException ex)
            {
                Loggers.GetIntance().Db.Error($"SQL Server Error in CreateDispenserDetailIfNotExists: {ex.Message}", ex);
                ErrorsSender.Send(ex, "CreateDispenserDetailIfNotExists");
                throw;
            }
        }

        public void CreateDispenserDetailIfNotExists(string kind, long withdrawalId, long dispenserId, DateTime created)
        {
            CreateDispenserDetailIfNotExists(kind, withdrawalId, dispenserId, created, 1);
        }

        void IStorage.CreateInvoicePayment(string kind, InvoicePayment paymentDetails)
        {
            throw new NotImplementedException();
        }
    }

}
