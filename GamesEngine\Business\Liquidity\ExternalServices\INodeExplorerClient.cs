﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NBitcoin;

namespace GamesEngine.Business.Liquidity.ExternalServices
{
    public interface INodeExplorerClient
    {
        Task<decimal> AddressBalanceAsync(string address, string currencyCode);
        Task<NodeExplorerBalanceInfo> AddressBalanceInfoAsync(string address, string currencyCode);
        Task<NodeUtxoResponse> GetUTXOsAsync(string descriptor);
        Task<FeeRate> GetFeeRateAsync();
        Task<BroadcastResult> BroadcastTxAsync(Transaction tx);
        Task<PSBT> GeneratePSBTAsync(string recipientAddressStr, Money amountToSend);
    }
}
