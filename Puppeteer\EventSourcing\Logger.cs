﻿using log4net;
using log4net.Appender;
using log4net.Layout;
using System;
using System.Collections.Generic;
using System.Threading;

namespace Puppeteer.EventSourcing
{
	public sealed class Loggers
	{
		public static Loggers loggers = new Loggers();

		private Loggers()
		{
            Kyc = new Logger();
            Drivers = new Logger();
			Apisix = new Logger();
            WebHook = new Logger();
            Sentinel = new Logger();
            Db = new Logger();
			Smart = new Logger();
			AccountingServicesASI = new Logger();
			AccountingServicesASIRemoveTransaction = new Logger();
			AccountingServicesASIPostTransaction = new Logger();
			AccountingServicesASIPostTransactionWRef = new Logger();
			AccountingServicesASIPostFreeFormTicket = new Logger();
			AccountingServicesASIPostFreeFormWagerCollection = new Logger();
			AccountingServicesASIPostFreeFormTicketAndWagers = new Logger();
			AccountingServicesASIGradeFreeFormWagerCollection = new Logger();
			AccountingServicesASISvcValidateCustomer = new Logger();
			AccountingServicesASIGetLottoCustomer = new Logger();
			AccountingServicesASIGetTicketWagers = new Logger();

			AccountingServicesInfo = new Logger();
			AccountingServicesGetToken = new Logger();
			AccountingServicesDeposit = new Logger();
			AccountingServicesWithdraw = new Logger();
			AccountingServicesValidateCustomer = new Logger();
			AccountingServicesAuthorizationCreateFragment = new Logger();
			AccountingServicesGradeUpdateFragment = new Logger();

			AccountingConsignmentDeposit = new Logger();
			AccountingConsignmentWithdraw = new Logger();
			AccountingConsignmentGetCommittedNames = new Logger();
			AccountingConsignmentGetDepositInfo = new Logger();
			AccountingConsignmentGetDepositUpdate = new Logger();
			AccountingConsignmentGetName = new Logger();
			AccountingConsignmentGetPayoutDetail = new Logger();
			AccountingConsignmentGetPayoutInfo = new Logger();
			AccountingConsignmentGetPayoutUpdate = new Logger();
			AccountingConsignmentGetSenders = new Logger();
			AccountingConsignmentGetDeposits = new Logger();
			AccountingConsignmentGetPayouts = new Logger();

			Emails = new Logger();
			Jobs = new Logger();
			SearchEngine = new Logger();
            NodeExplorer = new Logger();
            Scripts = new Logger();
			Betfair = new Logger();
		}

        public Logger Kyc { get; set; }
        public Logger Drivers { get; set; }
		public Logger Apisix { get; set; }
		public Logger WebHook { get; set; }
		public Logger Sentinel { get; set; }
		public Logger Smart { get; set; }
		public Logger Db { get; set; }
		public Logger AccountingServicesASI { get; set; }
		public Logger AccountingServicesASIRemoveTransaction { get; set; }
		public Logger AccountingServicesASIPostTransaction { get; set; }
		public Logger AccountingServicesASIPostTransactionWRef { get; set; }
		public Logger AccountingServicesASIPostFreeFormTicket { get; set; }
		public Logger AccountingServicesASIPostFreeFormWagerCollection { get; set; }
		public Logger AccountingServicesASIPostFreeFormTicketAndWagers { get; set; }
		public Logger AccountingServicesASIGradeFreeFormWagerCollection { get; set; }
		public Logger AccountingServicesASISvcValidateCustomer { get; set; }
		public Logger AccountingServicesASIGetLottoCustomer { get; set; }
		public Logger AccountingServicesASIGetTicketWagers { get; set; }

		public Logger AccountingServicesInfo { get; set; }
		public Logger AccountingServicesGetToken { get; set; }
		public Logger AccountingServicesDeposit { get; set; }
		public Logger AccountingServicesWithdraw { get; set; }
		public Logger AccountingServicesValidateCustomer { get; set; }
		public Logger AccountingServicesAuthorizationCreateFragment { get; set; }
		public Logger AccountingServicesGradeUpdateFragment { get; set; }

        public Logger AccountingConsignmentDeposit { get; set; }
		public Logger AccountingConsignmentWithdraw { get; set; }
		public Logger AccountingConsignmentGetCommittedNames { get; set; }
		public Logger AccountingConsignmentGetDepositInfo { get; set; }
		public Logger AccountingConsignmentGetDepositUpdate { get; set; }
		public Logger AccountingConsignmentGetName { get; set; }
		public Logger AccountingConsignmentGetPayoutDetail { get; set; }
		public Logger AccountingConsignmentGetPayoutInfo { get; set; }
		public Logger AccountingConsignmentGetPayoutUpdate { get; set; }
		public Logger AccountingConsignmentGetSenders { get; set; }
		public Logger AccountingConsignmentGetDeposits { get; set; }
		public Logger AccountingConsignmentGetPayouts { get; set; }

        public Logger Emails { get; set; }
		public Logger Jobs { get; set; }
		public Logger Betfair { get; set; }
		public Logger SearchEngine { get; set; }
		public Logger NodeExplorer { get; set; }
		public Logger Scripts { get; set; }

		public string LoggerDirectory { get; set; } = "/tmp/drivers";

        private Dictionary<string, Logger> loggerMap = new Dictionary<string, Logger>();
        public Logger GetOrCreateLogger(string driverId)
		{
			if (string.IsNullOrWhiteSpace(driverId)) throw new ArgumentNullException(nameof(driverId));
			
            if (!loggerMap.TryGetValue(driverId, out var logger))
			{
				string fileName = $"{driverId}.log";
                logger = new Logger(fileName, $"{LoggerDirectory}/{driverId}");
                loggerMap[driverId] = logger;
            }
			return logger;
        }

        public static Loggers GetIntance()
		{
			return loggers;
		}
	}

	public sealed class Logger
	{
        private ILog logger;

        public Logger(string loggerName = null, string loggerPath = null)
        {
            if (!string.IsNullOrEmpty(loggerName) && !string.IsNullOrEmpty(loggerPath))
            {
                // Configura un FileAppender específico para este logger
                var logRepository = LogManager.GetRepository(System.Reflection.Assembly.GetEntryAssembly());

                var fileAppender = new FileAppender
                {
                    Name = $"{loggerName}FileAppender",
                    File = loggerPath,
                    AppendToFile = true,
                    Layout = new PatternLayout("%date [%thread] %-5level %logger - %message%newline"),
                    LockingModel = new FileAppender.MinimalLock()
                };
                fileAppender.ActivateOptions();

                // Asigna el appender al logger
                var hierarchy = (log4net.Repository.Hierarchy.Hierarchy)logRepository;
                var loggerImpl = hierarchy.GetLogger(loggerName) as log4net.Repository.Hierarchy.Logger;
                if (loggerImpl != null)
                {
                    loggerImpl.AddAppender(fileAppender);
                }

                logger = LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), loggerName);
            }
            else
            {
                // Logger por defecto
                logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
            }
        }

        public void Error(string message, Exception e)
		{
			ThreadPool.QueueUserWorkItem(task => logger.Error(message, e));
		}

		public void Debug(string message)
		{
			ThreadPool.QueueUserWorkItem(task => logger.Debug(message));
		}

		public void SetLogger(ILog log)
		{
			this.logger = log;
		}
    }
}
