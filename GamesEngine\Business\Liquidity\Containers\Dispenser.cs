﻿using GamesEngine.Business.Liquidity.Sentinels.Outbound;
using GamesEngine.Business.Liquidity.Transactions;
using GamesEngine.Domains;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Business.Liquidity.PaymentEngineDocks;

namespace GamesEngine.Business.Liquidity.Containers
{
    internal abstract class Dispenser : Container
    {
        internal bool Enabled { get; private set; } = true;

        internal DateTime TargetDate { get; private set; } = DateTime.MinValue;

        internal Dispenser? Parent { get; private set; }

        private readonly List<EnclosureWithdrawal> enclosureWithdrawals;
        private readonly List<Dispenser> dispensers;

        internal Dispenser(int id, string name, string description, DateTime createdDate, Liquid liquid, string kind, int version, DateTime targetDate, List<EnclosureWithdrawal> withdrawals, List<Dispenser> dispensers)
            : base(id, kind, liquid, name, description, createdDate)
        {
            if (liquid == null) throw new ArgumentNullException(nameof(liquid));
            if (withdrawals == null) throw new ArgumentNullException(nameof(withdrawals));
            if (dispensers == null) throw new ArgumentNullException(nameof(dispensers));

            this.version = version;
            TargetDate = targetDate;
            this.enclosureWithdrawals = withdrawals;
            this.dispensers = dispensers;
            RecalculateAmount();
        }

        internal Dispenser(int id, string name, string description, DateTime createdDate, Liquid liquid, string kind, int version, DateTime targetDate, List<EnclosureWithdrawal> withdrawals, List<Dispenser> dispensers, string color) 
            : this(id, name, description, createdDate, liquid, kind, version, targetDate, withdrawals, dispensers)
        {
            if(string.IsNullOrWhiteSpace(color)) throw new ArgumentNullException(nameof(color));
            SetColor(color);
            if (targetDate != DateTime.MinValue)
            {
                AddTargetDate(createdDate, targetDate);
            }
        }

        internal string Path
        {
            get
            {
                if (Parent == null) return $"/{Name}";
                return $"{Parent.Path}/{Name}";
            }
        }

        internal IEnumerable<ParentInfoDto> ParentChain
        {
            get
            {
                var result = new List<ParentInfoDto>();
                var current = this.Parent;
                while (current != null)
                {
                    result.Add(new ParentInfoDto(current.Id, current.Name));
                    current = current.Parent;
                }
                result.Reverse();
                return result;
            }
        }

        internal void MoveToDispensers(List<int> list)
        {
            if (list == null || !list.Any()) throw new ArgumentNullException(nameof(list));

            HashSet<Dispenser> dispensersToUpdate = new HashSet<Dispenser>();

            var newDelegatedDispenser = Delegate() as Dispenser;

            foreach (var dispenserId in list)
            {
                var targetDispenser = Liquid.Outlet.FindDispenser(dispenserId);
                if (targetDispenser == null) throw new GameEngineException($"Tank with ID {dispenserId} does not exist.");
                if (targetDispenser.Kind != this.Kind) throw new GameEngineException($"Cannot move to a dispenser of a different kind. Expected: {this.Kind}, Found: {targetDispenser.Kind}.");
                if (targetDispenser is not DispenserReady tankReady) throw new GameEngineException("Cannot move to a dispenser that is not ready.");
                newDelegatedDispenser.MoveToDispenser(targetDispenser);
            }

            dispensersToUpdate.Add(newDelegatedDispenser);
            foreach (var dispenser in dispensersToUpdate)
            {
                dispenser.RecalculateAmount();
                DispenserHaveBeenMoveToDispenserEvent eventMsg = new DispenserHaveBeenMoveToDispenserEvent(dispenser.Id);
                PlatformMonitor.GetInstance().WhenNewEvent(eventMsg);
            }
        }

        private void MoveToDispenser(Dispenser newDispenser)
        {
            if (newDispenser == null) throw new ArgumentNullException(nameof(newDispenser));
            if (newDispenser == this) throw new GameEngineException("Cannot move to the same tank.");

            bool isBeenExecuted = Liquid.Outlet.IsDispenserInUse(newDispenser);
            if (isBeenExecuted) throw new GameEngineException("Cannot move to a dispenser that is currently in use.");

            if (newDispenser.ContainsRecursive(this)) throw new GameEngineException("Cannot move to a dispenser that is already a parent of this dispenser.");

            var newDelegatedDispenser = Delegate() as Dispenser;

            var currentParent = newDispenser.Parent;
            currentParent?.dispensers.Remove(newDispenser);

            newDispenser.Parent = newDelegatedDispenser;
            newDelegatedDispenser.dispensers.Add(newDispenser);
        }

        internal IEnumerable<Dispenser> DescendantDispensers(bool includeThis)
        {
            List<Dispenser> dispenserDescendants = new List<Dispenser>();
            if (includeThis) dispenserDescendants.Add(this);

            foreach (var childDispenser in this.dispensers)
            {
                dispenserDescendants.Add(childDispenser);

                foreach (var sub in childDispenser.DescendantDispensers(false))
                {
                    dispenserDescendants.Add(sub);
                }
            }
            return dispenserDescendants;
        }

        internal IEnumerable<Withdrawal> Withdrawals
        {
            get
            {
                var result = new List<Withdrawal>();
                for (int i = 0; i < enclosureWithdrawals.Count; i++)
                {
                    var withdrawal = enclosureWithdrawals[i].Withdrawal;
                    result.Add(withdrawal);
                }
                return result;
            }
        }

        internal IEnumerable<EnclosureWithdrawal> ExplandedEnclosureWithdrawals
        {
            get
            {
                List<EnclosureWithdrawal> withdrawalsResult = new List<EnclosureWithdrawal>();
                if (this.enclosureWithdrawals != null)
                {
                    foreach (var _w in this.enclosureWithdrawals)
                    {
                        if (_w != null)
                        {
                            withdrawalsResult.Add(_w);
                        }
                    }
                }

                if (this.dispensers != null)
                {
                    foreach (var dispenser in this.dispensers)
                    {
                        withdrawalsResult.AddRange(dispenser.ExplandedEnclosureWithdrawals);
                    }
                }
                return withdrawalsResult;
            }
        }

        internal IEnumerable<Withdrawal> ExplandedWithdrawals
        {
            get
            {
                List<Withdrawal> withdrawalsResult = new List<Withdrawal>();
                if (this.enclosureWithdrawals != null)
                {
                    foreach (var _w in this.enclosureWithdrawals)
                    {
                        if (_w != null)
                        {
                            withdrawalsResult.Add(_w.Withdrawal);
                        }
                    }
                }

                if (this.dispensers != null)
                {
                    foreach (var dispenser in this.dispensers)
                    {
                        withdrawalsResult.AddRange(dispenser.ExplandedWithdrawals);
                    }
                }
                return withdrawalsResult;
            }
        }

        internal bool ContainsWithdrawal(Withdrawal withdrawal)
        {
            if (withdrawal == null) throw new ArgumentNullException(nameof(withdrawal));

            return enclosureWithdrawals.Any(w => w.Withdrawal == withdrawal);
        }

        internal EnclosureWithdrawal FindEnclosureWithdrawal(Withdrawal withdrawal)
        {
            if (withdrawal == null) throw new ArgumentNullException(nameof(withdrawal));
            
            var enclosureWithdrawal = enclosureWithdrawals.FirstOrDefault(w => w.Withdrawal == withdrawal);
            if (enclosureWithdrawal == null) throw new GameEngineException("Withdrawal not found in this dispenser.");
            return enclosureWithdrawal;
        }

        internal EnclosureWithdrawal FindEnclosureWithdrawal(int withdrawalId)
        {
            if(withdrawalId == 0) throw new ArgumentNullException(nameof(withdrawalId));

            var enclosureWithdrawal = enclosureWithdrawals.FirstOrDefault(w => w.Withdrawal.Id == withdrawalId);
            if (enclosureWithdrawal == null) throw new GameEngineException("Withdrawal not found in this dispenser.");
            return enclosureWithdrawal;
        }

        internal void MoveWithdrawal(int dispenserId, Withdrawal withdrawal)
        {
            if (withdrawal == null) throw new ArgumentNullException(nameof(withdrawal));
            if (dispenserId <= 0) throw new ArgumentNullException(nameof(dispenserId));
            
            if (ContainsWithdrawal(withdrawal))
            {
                var newDelegatedDispenser = Delegate() as Dispenser;

                EnclosureWithdrawal enclosureWithdrawal = FindEnclosureWithdrawal(withdrawal);
                newDelegatedDispenser.enclosureWithdrawals.Remove(enclosureWithdrawal);
                newDelegatedDispenser.RecalculateAmount();
                DispenserWithdrawalHasBeenMoveEvent eventHasBeenMove = new DispenserWithdrawalHasBeenMoveEvent(Id);
                PlatformMonitor.GetInstance().WhenNewEvent(eventHasBeenMove);

                Dispenser dispenser = Liquid.Outlet.FindDispenser(dispenserId);
                Dispenser delegatedDispenser = dispenser.Delegate() as Dispenser;

                delegatedDispenser.enclosureWithdrawals.Add(enclosureWithdrawal);
                delegatedDispenser.RecalculateAmount();

                DispenserWithdrawalsAddedEvent dispenserWithdrawalsAddedEvent = new DispenserWithdrawalsAddedEvent(dispenser.Id);
                PlatformMonitor.GetInstance().WhenNewEvent(dispenserWithdrawalsAddedEvent);
            }
            else
            {
                throw new GameEngineException("Withdrawal not found in this dispenser.");
            }
        }

        internal bool HasWithdrawal(int withdrawalId)
        {
            if (withdrawalId <= 0) throw new ArgumentNullException(nameof(withdrawalId));
            return enclosureWithdrawals.Any(w => w.Withdrawal.Id == withdrawalId);
        }

        internal void AddTargetDate(DateTime now, DateTime targetDate)
        {
            if (targetDate == DateTime.MinValue) throw new ArgumentNullException(nameof(targetDate));
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));
            if (targetDate < DateTime.MinValue) throw new GameEngineException("Target date cannot be less than DateTime.MinValue.");
            if (targetDate < now) throw new GameEngineException("Target date cannot be in the past.");
            this.TargetDate = targetDate;

            TankHeaderInfoHasChangeEvent eventMsg = new TankHeaderInfoHasChangeEvent(Id);
            PlatformMonitor.GetInstance().WhenNewEvent(eventMsg);
        }

        internal class FlatterDispenser : Objeto
        {
            public int Id { get; private set; }
            public string Name { get; private set; }
            public string Path { get; private set; }
            public decimal Amount { get; private set; }
            public decimal ReceivedAmount { get; private set; }
            public string Description { get; private set; }
            public string ContainerColor { get; private set; }
            public FlatterDispenser(int id, string name, decimal currentAmount, decimal receivedAmount, string description, string containerColor, string path)
            {
                if (id <= 0) throw new ArgumentOutOfRangeException(nameof(id), "Id must be greater than zero.");
                if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name), "Name cannot be null or empty.");
                if (currentAmount < 0) throw new ArgumentOutOfRangeException(nameof(currentAmount), "Current amount cannot be negative.");
                if (receivedAmount < 0) throw new ArgumentOutOfRangeException(nameof(receivedAmount), "ReceivedAmount amount cannot be negative.");

                if (string.IsNullOrWhiteSpace(path)) throw new ArgumentNullException(nameof(path), "Path cannot be null or empty.");
                Id = id;
                Name = name;
                Path = path;
                Amount = currentAmount;
                ReceivedAmount = receivedAmount;
                Description = description;
                ContainerColor = containerColor;
            }
        }

        internal IEnumerable<FlatterDispenser> FlattenedList
        {
            get
            {
                List<FlatterDispenser> result = new List<FlatterDispenser>();
                if (dispensers != null)
                {
                    foreach (var child in dispensers)
                    {
                        var flattenedList = GetFlattenedList(child, $"/{Name}");
                        result.AddRange(flattenedList);
                    }
                }
                return result;
            }
        }

        private IEnumerable<FlatterDispenser> GetFlattenedList(Dispenser current, string name)
        {
            string currentPath = string.IsNullOrEmpty(name) ? current.Name : $"{name}/{current.Name}";

                yield return new FlatterDispenser(current.Id, current.Name, current.Amount,current.ReceivedAmount,current.Description,current.ContainerColor, currentPath);

            if (this.dispensers != null)
            {
                foreach (var child in current.dispensers)
                {
                    foreach (var item in GetFlattenedList(child, currentPath))
                    {
                        yield return item;
                    }
                }
            }
        }

        internal bool ContainsRecursive(Dispenser target)
        {
            if (dispensers.Any(t => t.Id == target.Id)) return true;

            return dispensers.Any(c => c.ContainsRecursive(target));
        }

        internal DispenserSummary BuildMonthlySummary(DateTime startDate,DateTime endDate,string name = null, FilterContainerType type = FilterContainerType.ALL, string color = null)
        {
            IEnumerable<Dispenser> filteredDispensers = Enumerable.Empty<Dispenser>();
            if (type == FilterContainerType.ALL || type == FilterContainerType.CONTAINER)
            {
                filteredDispensers = this.dispensers.Where(d => d is not DispenserInbox);

                if (startDate != DateTime.MinValue)
                    filteredDispensers = filteredDispensers.Where(d => d.CreatedAt >= startDate);
                if (endDate != DateTime.MaxValue)
                    filteredDispensers = filteredDispensers.Where(d => d.CreatedAt <= endDate.AddDays(1));
                if (!string.IsNullOrWhiteSpace(name))
                    filteredDispensers = filteredDispensers.Where(d => d.Name.IndexOf(name, StringComparison.OrdinalIgnoreCase) >= 0);
                if (!string.IsNullOrWhiteSpace(color))
                    filteredDispensers = filteredDispensers.Where(d => d.ContainerColor.Equals(color, StringComparison.OrdinalIgnoreCase));
            }
            int totalDispensers = filteredDispensers.Count();

            IEnumerable<Withdrawal> allWithdrawals = Enumerable.Empty<Withdrawal>();
            var inbox = this.dispensers.OfType<DispenserInbox>().FirstOrDefault();
            if (type == FilterContainerType.ALL || type == FilterContainerType.TRANSACTION)
            {
                allWithdrawals = this.Withdrawals;
                if (inbox != null)
                    allWithdrawals = allWithdrawals.Concat(inbox.ExplandedWithdrawals);

                if (startDate != DateTime.MinValue)
                    allWithdrawals = allWithdrawals.Where(w => w.CreatedAt >= startDate);
                if (endDate != DateTime.MaxValue)
                    allWithdrawals = allWithdrawals.Where(w => w.CreatedAt <= endDate.AddDays(1));
              
            }

            var dispenserGroups = filteredDispensers
                .GroupBy(d => d.CreatedAt.ToString("MM/yyyy"))
                .ToDictionary(g => g.Key, g => g.ToList());

            var withdrawalGroups = allWithdrawals
                .GroupBy(w => w.CreatedAt.ToString("MM/yyyy"))
                .ToDictionary(g => g.Key, g => g.ToList());

            var allMonthKeys = dispenserGroups.Keys
                .Union(withdrawalGroups.Keys)
                .OrderBy(k => k)
                .ToList();

            var monthlySummaries = new List<MonthlyDispenserSummary>();

            foreach (var key in allMonthKeys)
            {
                var dispensersInMonth = (type == FilterContainerType.TRANSACTION)
                    ? new List<Dispenser>()
                    : (dispenserGroups.ContainsKey(key) ? dispenserGroups[key] : new List<Dispenser>());

                var withdrawalsInMonth = (type == FilterContainerType.CONTAINER)
                    ? new List<Withdrawal>()
                    : (withdrawalGroups.ContainsKey(key) ? withdrawalGroups[key] : new List<Withdrawal>());

                var dispenserDetails = dispensersInMonth
                    .Select(d => new FlatterDispenser(d.Id, d.Name, d.Amount, d.ReceivedAmount, d.Description, d.ContainerColor, d.Path))
                    .ToList();

                var withdrawalDetails = withdrawalsInMonth
                    .Select(w => new WithdrawalDetail(w.Id, w.Amount, w.ReceivedAmount, w.CreatedAt))
                    .ToList();

                var monthTotal = dispenserDetails.Sum(d => d.ReceivedAmount) + withdrawalDetails.Sum(w => w.ReceivedAmount);

                monthlySummaries.Add(new MonthlyDispenserSummary(key, monthTotal, withdrawalDetails, dispenserDetails));
            }

            return new DispenserSummary(this.Amount, this.ReceivedAmount, monthlySummaries, this.ExplandedWithdrawals.Count(), totalDispensers);
        }

        protected override DateTime CalculateMinDate()
        {
            
            DateTime result = DateTime.MaxValue;
            if (enclosureWithdrawals != null && enclosureWithdrawals.Count > 0)
            {
                result = enclosureWithdrawals.Min(enclosureWithdrawal => enclosureWithdrawal.CreatedAt);
            }
            if (dispensers != null && dispensers.Count > 0)
            {
                foreach (var dispenser in dispensers)
                {

                    DateTime childMinDate = dispenser.MinDate;
                    if (childMinDate < result)
                    {
                        result = childMinDate;
                    }
                }
            }

            return result;
        }

        protected override DateTime CalculateMaxDate()
        {

            DateTime result = DateTime.MinValue;
            if (enclosureWithdrawals != null && enclosureWithdrawals.Count > 0)
            {
                result = enclosureWithdrawals.Max(ew => ew.CreatedAt);
            }
            if (dispensers != null && dispensers.Count > 0)
            {
                foreach (var dispenser in dispensers)
                {
                    DateTime childMaxDate = dispenser.MaxDate;
                    if (childMaxDate > result)
                    {
                        result = childMaxDate;
                    }
                }
            }
            return result;
        }

        internal bool BuildWithdrawalsBetween(DateTime startDate, DateTime endDate)
        {
            if (startDate == DateTime.MinValue && endDate == DateTime.MinValue) throw new ArgumentNullException(nameof(startDate), "Both 'from' and 'to' dates cannot be MinValue.");

            foreach (var withdrawal in Withdrawals)
            {
                if (withdrawal.CreatedAt >= startDate && withdrawal.CreatedAt <= endDate)
                {
                    return true;
                }
            }
            return false;
        }

        internal bool BuildWithdrawalsFrom(DateTime startDate)
        {
            if (startDate == DateTime.MinValue) throw new ArgumentNullException(nameof(startDate));
            return BuildWithdrawalsBetween(startDate, DateTime.MaxValue);
        }

        internal bool BuildWithdrawalsUpTo(DateTime endDate)
        {
            if (endDate == DateTime.MinValue) throw new ArgumentNullException(nameof(endDate));
            return BuildWithdrawalsBetween(DateTime.MinValue, endDate);
        }

        public class WithdrawalDetail : Objeto
        {
            public int Id { get; }
            public decimal Amount { get; }
            public decimal ReceivedAmount { get; }
            public string CreatedAt { get; }

            public WithdrawalDetail(int id, decimal amount,decimal receivedAmount, DateTime createdAt)
            {
                Id = id;
                Amount = amount;
                ReceivedAmount = receivedAmount;
                CreatedAt = createdAt.ToString("MM/dd/yyyy HH:mm:ss");
            }
        }

        public class MonthlyDispenserSummary : Objeto
        {
            public string Month { get; }
            public decimal MonthTotal { get; }
            public IEnumerable<WithdrawalDetail> Withdrawals { get; }
            public IEnumerable<FlatterDispenser> DispenserDetail { get; }

            public MonthlyDispenserSummary(string month, decimal monthTotal, IEnumerable<WithdrawalDetail> withdrawals, IEnumerable<FlatterDispenser> dispenserDetails)
            {
                Month = month;
                MonthTotal = monthTotal;
                Withdrawals = withdrawals ?? new List<WithdrawalDetail>();
                DispenserDetail = dispenserDetails ?? new List<FlatterDispenser>();
            }
        }

        public class DispenserSummary : Objeto
        {
            public decimal DispenserTotalAmount { get; }
            public decimal TotalReceivedAmount { get; }
            public int TotalWithdrawals { get; }
            public int TotalDispensers { get; }
            public IEnumerable<MonthlyDispenserSummary> MonthlySummaries { get; }

            public DispenserSummary(decimal totalAmount,decimal totalReceivedAmount, IEnumerable<MonthlyDispenserSummary> monthlySummaries,int totalWithdrawals, int totalDispensers)
            {
                DispenserTotalAmount = totalAmount;
                TotalReceivedAmount = totalReceivedAmount;
                MonthlySummaries = monthlySummaries;
                TotalDispensers = totalDispensers;
                TotalWithdrawals = totalWithdrawals;
            }
        }

        internal class DispenserInbox : Dispenser
        {
            internal DispenserInbox(string kind, Liquid liquid, DateTime createdAt) : base(int.MaxValue, "Inbox", "Inbox for incoming transactions", createdAt, liquid, kind, 1, DateTime.MaxValue, new List<EnclosureWithdrawal>(), new List<Dispenser>())
            {
            }

            private DispenserInbox(int id, string name, string description, DateTime createdDate, Liquid liquid, string kind, int version, DateTime startDate, List<EnclosureWithdrawal> withdrawals, List<Dispenser> dispensers) :
                base(id, name, description, createdDate, liquid, kind, version, startDate, withdrawals, dispensers)
            {
            }

            protected override DispenserInbox Delegate()
            {
                DispenserInbox dispenserInbox = new DispenserInbox(Id, Name, Description, CreatedAt, Liquid, Kind, Version + 1, TargetDate, enclosureWithdrawals, dispensers);

                dispenserInbox.Parent = Parent;
                if (dispenserInbox.Parent != null)
                {
                    Parent.dispensers.Remove(this);
                    Parent.dispensers.Add(dispenserInbox);
                }

                foreach (var _dispenser in this.dispensers)
                {
                    if (_dispenser.Parent == this) _dispenser.Parent = dispenserInbox;
                }

                dispenserInbox.previous = this;
                next = dispenserInbox;

                Liquid.Outlet.AddOrUpdateDispenser(dispenserInbox);
                return dispenserInbox;
            }

            internal Withdrawal CreateWithdrawal(DateTime now, int withdrawalId, string pullPaymentId, int authorization, decimal amount, string destionation, string atAddress, Domain domain, int storeId, string externalReference)
            {
                if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));
                if (withdrawalId <= 0) throw new ArgumentNullException(nameof(withdrawalId));
                if (string.IsNullOrWhiteSpace(pullPaymentId)) throw new ArgumentNullException(nameof(pullPaymentId));
                if (authorization <= 0) throw new ArgumentNullException(nameof(authorization));
                if (amount <= 0) throw new ArgumentNullException(nameof(amount));
                if (string.IsNullOrWhiteSpace(destionation)) throw new ArgumentNullException(nameof(destionation));
                if (string.IsNullOrWhiteSpace(atAddress)) throw new ArgumentNullException(nameof(atAddress));
                if (domain == null) throw new ArgumentNullException(nameof(domain));
                if (storeId == 0) throw new ArgumentNullException(nameof(storeId)); 
                if (string.IsNullOrWhiteSpace(externalReference)) throw new ArgumentNullException(nameof(externalReference));

                var withdrawal = new Withdrawal(withdrawalId, Kind, pullPaymentId, authorization, amount, now, destionation, Id, atAddress, domain, storeId, externalReference);
                EnclosureWithdrawal enclosureWithdrawal = new EnclosureWithdrawal(withdrawal, Id);
                enclosureWithdrawals.Add(enclosureWithdrawal);

                DispenserWithdrawalsAddedEvent dispenserWithdrawalsAddedEvent = new DispenserWithdrawalsAddedEvent(Id);
                PlatformMonitor.GetInstance().WhenNewEvent(dispenserWithdrawalsAddedEvent);

                return withdrawal;
            }

            internal Withdrawal CreateWithdrawal(DateTime now, int withdrawalId, string pullPaymentId, int authorization, decimal amount, string destionation, string atAddress, Domain domain, int storeId, string externalReference, decimal rate, decimal receivedAmount, string receivedCurrency)
            {
                if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));
                if (withdrawalId <= 0) throw new ArgumentNullException(nameof(withdrawalId));
                if (string.IsNullOrWhiteSpace(pullPaymentId)) throw new ArgumentNullException(nameof(pullPaymentId));
                if (authorization <= 0) throw new ArgumentNullException(nameof(authorization));
                if (amount <= 0) throw new ArgumentNullException(nameof(amount));
                if (string.IsNullOrWhiteSpace(destionation)) throw new ArgumentNullException(nameof(destionation));
                if (string.IsNullOrWhiteSpace(atAddress)) throw new ArgumentNullException(nameof(atAddress));
                if (domain == null) throw new ArgumentNullException(nameof(domain));
                if (storeId == 0) throw new ArgumentNullException(nameof(storeId));
                if (string.IsNullOrWhiteSpace(externalReference)) throw new ArgumentNullException(nameof(externalReference));
                if (receivedAmount <= 0) throw new ArgumentNullException(nameof(receivedAmount));
                if (string.IsNullOrWhiteSpace(receivedCurrency)) throw new ArgumentNullException(nameof(receivedCurrency));

                var withdrawal = new Withdrawal(withdrawalId, Kind, pullPaymentId, authorization, amount, now, destionation, Id, atAddress, domain, storeId, externalReference, rate, receivedAmount, receivedCurrency);
                EnclosureWithdrawal enclosureWithdrawal = new EnclosureWithdrawal(withdrawal, Id);
                enclosureWithdrawals.Add(enclosureWithdrawal);

                DispenserWithdrawalsAddedEvent dispenserWithdrawalsAddedEvent = new DispenserWithdrawalsAddedEvent(Id);
                PlatformMonitor.GetInstance().WhenNewEvent(dispenserWithdrawalsAddedEvent);

                return withdrawal;
            }

            internal void CancelWithdrawal(DateTime now, int withdrawalId)
            {
                EnclosureWithdrawal enclosureWithdrawal = enclosureWithdrawals.FirstOrDefault(ew => ew.Id == withdrawalId);
                if (enclosureWithdrawal == null) throw new GameEngineException($"Withdrawal with ID {withdrawalId} not found in the inbox.");
                if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));
                enclosureWithdrawal.SetNotes($"Withdrawal cancelled on {now}.");

                WithdrawalCanceledEvent cancelWithdrawalEvent = new WithdrawalCanceledEvent(Id);
                PlatformMonitor.GetInstance().WhenNewEvent(cancelWithdrawalEvent);
            }

            static DateTime noStartDateSentinel = new DateTime(1951, 1, 1);
            static DateTime noEndDateSentinel = new DateTime(2049, 12, 31);
            internal IEnumerable<Withdrawal> WithdrawalsBy(DateTime startDate = default, DateTime endDate = default)
            {
                if (endDate < startDate) throw new ArgumentException("End date cannot be earlier than start date.", nameof(endDate));
                if (startDate == noStartDateSentinel && endDate == noEndDateSentinel) return enclosureWithdrawals.Select(w => w.Withdrawal).ToList();

                var result = enclosureWithdrawals
                    .Where(w => w.CreatedAt >= startDate && w.CreatedAt <= endDate.AddDays(1))
                    .OrderByDescending(w => w.CreatedAt)
                    .Select(w => w.Withdrawal)
                    .ToList();
                return result;
            }

            internal DispenserReady CreateDispenserWithWithdrawal(int id, string name, string description, DateTime creaatedDate, DateTime startDate, List<EnclosureWithdrawal> newEnclosureWithdrawals)
            {
                if (id <= 0) throw new ArgumentNullException(nameof(id));
                DispenserReady dispenser = null;
                if (newEnclosureWithdrawals == null || !newEnclosureWithdrawals.Any())
                {
                    dispenser = new DispenserReady(id, name, description, creaatedDate, Liquid, Kind, 1, startDate);
                }
                else
                {
                    foreach (var enclosureWithdrawal in newEnclosureWithdrawals)
                    {
                        if (!this.enclosureWithdrawals.Contains(enclosureWithdrawal))
                        {
                            throw new GameEngineException($"Withdrawal with ID {enclosureWithdrawal.Id} does not belong to this dispenser.");
                        }
                        if (enclosureWithdrawal.IsClaimed)
                        {
                            throw new GameEngineException($"Withdrawal with ID {enclosureWithdrawal.Id} is not pending and cannot be moved.");
                        }
                        this.enclosureWithdrawals.Remove(enclosureWithdrawal);
                        RecalculateAmount();
                    }
                    dispenser = new DispenserReady(id, name, description, creaatedDate, Liquid, Kind, 1, startDate, newEnclosureWithdrawals);
                }

                Liquid.Outlet.AddOrUpdateDispenser(dispenser);

                if (Integration.UseKafka)
                {
                    using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(true, Integration.Kafka.TopicForContainerEvents))
                    {
                        var withdrawalIds = newEnclosureWithdrawals?.Select(w => w.Withdrawal.Id).ToList() ?? new List<int>();
                        CreatedDispenserMessage createdDispenserMessage = new CreatedDispenserMessage(dispenser.Id, dispenser.Kind, dispenser.Amount, dispenser.CreatedAt, dispenser.Version, withdrawalIds);
                        buffer.Send(createdDispenserMessage);
                    }
                }

                CreatedDispenserEvent createdDispenserEvent = new CreatedDispenserEvent(creaatedDate, dispenser.Id, dispenser.Name);
                PlatformMonitor.GetInstance().WhenNewEvent(createdDispenserEvent);
                return dispenser;
            }

            internal DispenserReady CreateDispenserWithWithDispenser(int id, string name, string description, DateTime createdDate, DateTime startDate,List<Dispenser> newDispensers)
            {
                if (id <= 0) throw new ArgumentNullException(nameof(id));
                if(newDispensers == null || !newDispensers.Any()) throw new ArgumentNullException(nameof(newDispensers));
                foreach (var currentDispenser in newDispensers)
                {
                    if(this.Liquid.Outlet.IsDispenserInUse(currentDispenser)) throw new GameEngineException($"Dispenser with ID {currentDispenser.Id} is currently in use and cannot be moved.");

                    RecalculateAmount();
                }
                DispenserReady dispenser = new DispenserReady(id, name, description, createdDate, Liquid, Kind, 1, startDate, newDispensers);

                Liquid.Outlet.AddOrUpdateDispenser(dispenser);

                if (Integration.UseKafka)
                {
                    using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(true, Integration.Kafka.TopicForContainerEvents))
                    {
                        var withdrawalIds = new List<int>(); // No withdrawals in this case
                        CreatedDispenserMessage createdDispenserMessage = new CreatedDispenserMessage(dispenser.Id, dispenser.Kind, dispenser.Amount, dispenser.CreatedAt, dispenser.Version, withdrawalIds);
                        buffer.Send(createdDispenserMessage);
                    }
                }

                CreatedDispenserEvent createdDispenserEvent = new CreatedDispenserEvent(createdDate, dispenser.Id, dispenser.Name);
                PlatformMonitor.GetInstance().WhenNewEvent(createdDispenserEvent);
                return dispenser;
            }
        }

        internal class DispenserReady : Dispenser
        {
            internal bool HasBeenCommitted => outboundTask != null;

            internal DispenserReady(int id, string name, string description,DateTime createdDate, Liquid liquid, string kind, int version, DateTime startDate) :
                base(id, name, description,createdDate, liquid, kind, version, startDate, new List<EnclosureWithdrawal>(), new List<Dispenser>())
            {
            }

            internal DispenserReady(int id, string name, string description, DateTime createdDate, Liquid liquid, string kind, int version, DateTime startDate, List<Dispenser> dispensers) :
                base(id, name, description, createdDate, liquid, kind, version, startDate, new List<EnclosureWithdrawal>(), dispensers)
            {
               
            }

            internal DispenserReady(int id, string name, string description, DateTime createdDate, Liquid liquid, string kind, int version, DateTime startDate, List<EnclosureWithdrawal> enclosureWithdrawals) :
                base(id, name, description, createdDate, liquid, kind, version, startDate, enclosureWithdrawals, new List<Dispenser>())
            {
            }

            private DispenserReady(int id, string name, string description, DateTime createdDate, Liquid liquid, string kind, int version, DateTime startDate, List<EnclosureWithdrawal> withdrawals, List<Dispenser> dispensers, string color, DateTime targetDate, OutboundTask outboundTask) :
                base(id, name, description, createdDate, liquid, kind, version, startDate, withdrawals, dispensers)
            {
                this.outboundTask = outboundTask;
                
                if (!string.IsNullOrWhiteSpace(color)) SetColor(color);
                if (targetDate != DateTime.MinValue) AddTargetDate(createdDate, targetDate);
            }

            protected override DispenserReady Delegate()
            {
                DispenserReady dispenserReady = new DispenserReady(Id, Name, Description, CreatedAt, Liquid, Kind, Version + 1, TargetDate, enclosureWithdrawals, dispensers, ContainerColor, TargetDate, outboundTask);
                
                dispenserReady.Parent = Parent;
                if (dispenserReady.Parent != null)
                {
                    Parent.dispensers.Remove(this);
                    Parent.dispensers.Add(dispenserReady);
                }

                foreach (var _dispenser in this.dispensers)
                {
                    if (_dispenser.Parent == this) _dispenser.Parent = dispenserReady;
                }

                dispenserReady.previous = this;
                next = dispenserReady;

                Liquid.Outlet.AddOrUpdateDispenser(dispenserReady);
                return dispenserReady;
            }

            internal void ClaimedWithdrawals(bool itIsThePresent, DateTime now, List<int> claimedWithdrawlIds)
            {
                if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));
                if (claimedWithdrawlIds == null || !claimedWithdrawlIds.Any()) throw new ArgumentNullException(nameof(claimedWithdrawlIds));
                
                List<EnclosureWithdrawal> unclaimedWithdrawals = new List<EnclosureWithdrawal>();
                foreach (var withdrawal in ExplandedEnclosureWithdrawals)
                {
                    bool isClaimed = claimedWithdrawlIds.Contains(withdrawal.Id);
                    if (!isClaimed)
                    {
                        unclaimedWithdrawals.Add(withdrawal);
                    }
                }

                foreach (var unclaimedWithdrawal in unclaimedWithdrawals)
                {
                    unclaimedWithdrawal.Cancel(itIsThePresent, now, cancelReason: $"Could not be confirmed, sentinel task timeout has force to cancel this withdrawal.");
                }
            }

            internal DispenserDiscarded Discard(DateTime discardDate)
            {
                if (discardDate == DateTime.MinValue) throw new ArgumentNullException(nameof(discardDate));
                var dispenserDiscarded = new DispenserDiscarded(this, discardDate);
                Liquid.Outlet.AddOrUpdateDispenser(dispenserDiscarded);

                if (Liquid.EgressSentinel.HasTask(this))
                {
                    var taskToCancel = Liquid.EgressSentinel.FindTask(this);
                    Liquid.EgressSentinel.Detach(taskToCancel);
                }

                return dispenserDiscarded;
            }

            internal decimal EstimateWithdrawalFee(decimal amount, TimeSpan allowedTime)
            {
                throw new NotImplementedException("This method should be implemented in the derived class.");
            }

            private OutboundTask outboundTask;
            internal void CommitWithdrawals(bool itIsThePresent, DateTime now, PaymentEngineDock paymentEngineDock)
            {
                if (outboundTask != null) throw new InvalidOperationException("There is already an outbound task in progress.");
                if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));
                if (paymentEngineDock == null) throw new ArgumentNullException(nameof(paymentEngineDock));

                outboundTask = Liquid.EgressSentinel.AwaitWithdrawalConfirmations(itIsThePresent, now, this, paymentEngineDock);

                DispenserWithdrawalsCommittedEvent dispenserWithdrawalsCommittedEvent = new DispenserWithdrawalsCommittedEvent(Id);
                PlatformMonitor.GetInstance().WhenNewEvent(dispenserWithdrawalsCommittedEvent);
            }

            internal void ConfirmWithdrawal(bool itIsThePresent, DateTime now, Withdrawal withdrawal, string payoutId, string paymentDockId)
            {
                if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));
                if (withdrawal == null) throw new ArgumentNullException(nameof(withdrawal));
                if (string.IsNullOrWhiteSpace(payoutId)) throw new ArgumentNullException(nameof(payoutId));
                if (string.IsNullOrWhiteSpace(paymentDockId)) throw new ArgumentNullException(nameof(paymentDockId));

                EnclosureWithdrawal enclosureWithdrawal = FindEnclosureWithdrawal(withdrawal);
                enclosureWithdrawal.Claim(itIsThePresent, now, Version);

                Liquid.EgressSentinel.ConfirmWithdrawal(this, withdrawal, payoutId, paymentDockId);
            }

            internal class DispatchedDispenserMessage : LiquidityEventMessage
            {
                internal string Kind { get; private set; }
                internal int DispenserId { get; private set; }
                internal DateTime DispatchedDate { get; private set; }
                internal List<int> DispathcedWithdrawls { get; private set; }

                internal DispatchedDispenserMessage(string kind, int dispenserId, DateTime dispatchedDate, List<int> dispathcedWithdrawls) : base(LiquidityMessageType.DispatchedDispenser)
                {
                    if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentNullException(nameof(kind));
                    if (dispenserId <= 0) throw new ArgumentNullException(nameof(dispenserId));
                    if (dispatchedDate == DateTime.MinValue) throw new ArgumentNullException(nameof(dispatchedDate));
                    if (dispathcedWithdrawls == null || !dispathcedWithdrawls.Any()) throw new ArgumentNullException(nameof(dispathcedWithdrawls));

                    Kind = kind;
                    DispenserId = dispenserId;
                    DispatchedDate = dispatchedDate;
                    DispathcedWithdrawls = dispathcedWithdrawls;
                }

                public DispatchedDispenserMessage(string message) : base(message)
                {
                }

                protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
                {
                    base.Deserialize(serializedMessage, out fieldOrder);
                    Kind = serializedMessage[fieldOrder++];
                    DispenserId = int.Parse(serializedMessage[fieldOrder++]);

                    string[] withdrawls = serializedMessage[fieldOrder++].Split(',', StringSplitOptions.RemoveEmptyEntries);
                    DispathcedWithdrawls = withdrawls.Select(w => int.Parse(w)).ToList();

                    int year = int.Parse(serializedMessage[fieldOrder++]);
                    int month = int.Parse(serializedMessage[fieldOrder++]);
                    int day = int.Parse(serializedMessage[fieldOrder++]);
                    int hour = int.Parse(serializedMessage[fieldOrder++]);
                    int minute = int.Parse(serializedMessage[fieldOrder++]);
                    int second = int.Parse(serializedMessage[fieldOrder++]);
                    DispatchedDate = new DateTime(year, month, day, hour, minute, second);
                }

                protected override void InternalSerialize()
                {
                    string dispathcedWithdrawlsComas = string.Join(",", DispathcedWithdrawls);

                    base.InternalSerialize();
                    AddProperty(Kind).
                    AddProperty(DispenserId).
                    AddProperty(dispathcedWithdrawlsComas).
                    AddProperty(DispatchedDate);
                }
            }
        }

        internal class DispenserDiscarded : Dispenser
        {
            private Dispenser lastDispenser;

            internal DateTime DiscardDate { get; private set; }

            internal DispenserDiscarded(DispenserReady dispenserReady, DateTime discardDate) :
                base(dispenserReady.Id, dispenserReady.Name, dispenserReady.Description, dispenserReady.CreatedAt, dispenserReady.Liquid, dispenserReady.Kind, dispenserReady.Version, dispenserReady.TargetDate, dispenserReady.enclosureWithdrawals, dispenserReady.dispensers, dispenserReady.ContainerColor)
            {
                if (dispenserReady == null) throw new ArgumentNullException(nameof(dispenserReady));
                if (discardDate == DateTime.MinValue) throw new ArgumentNullException(nameof(discardDate));
                this.lastDispenser = dispenserReady;
                DiscardDate = discardDate;

                DispenserDiscardedEvent dispenserDiscardedEvent = new DispenserDiscardedEvent(dispenserReady.Id);
                PlatformMonitor.GetInstance().WhenNewEvent(dispenserDiscardedEvent);
            }

            private DispenserDiscarded(int id, string name, string description, DateTime createdDate, Liquid liquid, string kind, int version, DateTime startDate, List<EnclosureWithdrawal> withdrawals, List<Dispenser> dispensers, string color, DateTime targetDate, DateTime discardDate, Dispenser lastDispenser) :
                base(id, name, description,createdDate, liquid, kind, version, targetDate, withdrawals, dispensers, color)
            {
                if (lastDispenser == null) throw new ArgumentNullException(nameof(lastDispenser));
                if (discardDate == DateTime.MinValue) throw new ArgumentNullException(nameof(discardDate));
                this.lastDispenser = lastDispenser;
                DiscardDate = discardDate;
            }

            protected override DispenserDiscarded Delegate()
            {
                DispenserDiscarded dispenserDiscarded = new DispenserDiscarded(Id, Name, Description, CreatedAt, Liquid, Kind, Version + 1, TargetDate, enclosureWithdrawals, dispensers, ContainerColor, TargetDate, DiscardDate, lastDispenser);
                
                dispenserDiscarded.Parent = Parent;
                if (dispenserDiscarded.Parent != null)
                {
                    Parent.dispensers.Remove(this);
                    Parent.dispensers.Add(dispenserDiscarded);
                }

                foreach (var _dispenser in this.dispensers)
                {
                    if (_dispenser.Parent == this) _dispenser.Parent = dispenserDiscarded;
                }

                dispenserDiscarded.previous = this;
                next = dispenserDiscarded;

                Liquid.Outlet.AddOrUpdateDispenser(dispenserDiscarded);
                return dispenserDiscarded;
            }
        }
    }

}
