﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <CompilerCondition>false</CompilerCondition>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="SocketsManager\**" />
    <Content Remove="SocketsManager\**" />
    <EmbeddedResource Remove="SocketsManager\**" />
    <None Remove="SocketsManager\**" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="..\ExchangeAPI\Controllers\CatalogConsumer.cs" Link="Controllers\CatalogConsumer.cs" />
	<Compile Include="..\MarchMadnessAPI\Controllers\AuthorizeController.cs" Link="Controllers\AuthorizeController.cs" />
	<Compile Include="..\MarchMadnessAPI\Controllers\DriverController.cs" Link="Controllers\DriverController.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="..\.dockerignore" Link=".dockerignore">
      <DependentUpon>$(DockerDefaultDockerfile)</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Elastic.Apm.NetCoreAll" Version="1.31.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.2" />
    <PackageReference Include="log4net" Version="3.0.4" />
    <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="9.0.2" />
    <PackageReference Include="NBitcoin" Version="8.0.18" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="7.3.1" />
    <PackageReference Include="Microsoft.OpenApi" Version="1.6.23" />
    <PackageReference Include="Swashbuckle.AspNetCore.Filters" Version="8.0.2" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\GamesEngineMocks\GamesEngineMocks.csproj" />
    <ProjectReference Include="..\GamesEngine\GamesEngine.csproj" />
  </ItemGroup>
  <Target Name="Compiler" AfterTargets="Build" Condition=" '$(CompilerCondition)' ">
    <!-- Directory/Current Project Configuration/Project Framework/Current Project/File Extension-->
    <Exec Command="dotnet run --project ./../RBuilder/RBuilder.csproj $(ProjectDir)../ $(ProjectName) csproj bin $(Configuration) $(TargetFramework) ExCopy 1" />
  </Target>
</Project>