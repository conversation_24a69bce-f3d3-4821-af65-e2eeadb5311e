using log4net;
using Puppeteer.EventSourcing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace LiquidityBIAPI
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var logRepository = log4net.LogManager.GetRepository(System.Reflection.Assembly.GetEntryAssembly());
            log4net.Config.XmlConfigurator.Configure(logRepository, new System.IO.FileInfo("log4net.config"));

            Loggers.GetIntance().Db.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "database"));
            Loggers.GetIntance().Emails.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "emails"));
            Loggers.GetIntance().SearchEngine.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "searchengine"));
            Loggers.GetIntance().NodeExplorer.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "nodeexplorer"));

            CreateHostBuilder(args).Build().Run();
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>().
                    UseUrls("http://0.0.0.0:5070").
                    ConfigureLogging((hostingContext, logging) =>
                    {
                        // Requires `using Microsoft.Extensions.Logging;`
                        logging.AddConfiguration(hostingContext.Configuration.GetSection("Logging"));
                        logging.AddConsole();
                        logging.AddDebug();
                        logging.AddEventSourceLogger();
                    });
                });
    }
}